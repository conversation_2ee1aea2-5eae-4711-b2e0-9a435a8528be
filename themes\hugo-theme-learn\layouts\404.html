<!DOCTYPE html>
<html lang="{{ .Page.Language | default "en" }}" class="js csstransforms3d">

<head>
  <meta charset="utf-8"> {{ partial "meta.html" . }} {{ partial "favicon.html" . }} {{ .Scratch.Add "title" "" }}{{ if eq .Site.Data.titles .Title }}{{ .Scratch.Set "title" (index .Site.Data.titles .Title).title }}{{ else }}{{ .Scratch.Set "title" .Title}}{{end}}
  <title>{{ .Scratch.Get "title" }}</title>

   {{ $assetBusting := not .Site.Params.disableAssetsBusting }}
    <link href="{{"css/nucleus.css" | relURL}}{{ if $assetBusting }}?{{ now.Unix }}{{ end }}" rel="stylesheet">
    <link href="{{"css/fontawesome-all.min.css" | relURL}}{{ if $assetBusting }}?{{ now.Unix }}{{ end }}" rel="stylesheet">
    <link href="{{"css/hybrid.css" | relURL}}{{ if $assetBusting }}?{{ now.Unix }}{{ end }}" rel="stylesheet">
    <link href="{{"css/featherlight.min.css" | relURL}}{{ if $assetBusting }}?{{ now.Unix }}{{ end }}" rel="stylesheet">
    <link href="{{"css/perfect-scrollbar.min.css" | relURL}}{{ if $assetBusting }}?{{ now.Unix }}{{ end }}" rel="stylesheet">
    <link href="{{"css/theme.css" | relURL}}{{ if $assetBusting }}?{{ now.Unix }}{{ end }}" rel="stylesheet">
    <link href="{{"css/hugo-theme.css" | relURL}}{{ if $assetBusting }}?{{ now.Unix }}{{ end }}" rel="stylesheet">
    {{with .Site.Params.themeVariant}}
      <link href="{{(printf "css/theme-%s.css" .) | relURL}}{{ if $assetBusting }}?{{ now.Unix }}{{ end }}" rel="stylesheet">
    {{end}}
<style>
    :root #header + #content > #left > #rlblock_left {
      display: none !important;
    }

    p,
    li,
    ul {
      text-align: center
    }

    ul {
      list-style-type: none;
    }
</style>
{{ partial "custom-header.html" . }}
</head>

  <body class="" data-url="/">

    <section id="body" style="margin-left:0px;">
      <div id="overlay"></div>
      <div id="chapter">
        <div id="body-inner">
          <h1>{{T "title-404"}}</h1>
          <p>
          </p>
          <p>{{T "message-404"}}</p>
          <p></p>
          <p><a href='{{ "" | relLangURL }}'>{{T "Go-to-homepage"}}</a></p>
          <p><img src='{{ "/images/gopher-404.jpg" | relURL }}' style="width:50%" alt="Page not found!"></p>
        </div>
      </div>

    </section>
  </body>

</html>
