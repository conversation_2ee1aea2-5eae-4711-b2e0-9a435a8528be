<div class="logo">
    <a href="{{ .Site.BaseURL }}">
        <h2>{{ .Site.Title }}</h2>
    </a>
</div>

<nav class="menu">
    <ul>
        {{ range .Site.Menus.main }}
        <li>
            <a href="{{ .URL | relURL }}">{{ .Name }}</a>
        </li>
        {{ end }}
    </ul>
    
    {{ $currentPage := . }}
    {{ range .Site.Home.Sections.ByWeight }}
        {{ template "section-tree" dict "sect" . "currentpage" $currentPage }}
    {{ end }}
</nav>

{{ define "section-tree" }}
{{ $currentPage := .currentpage }}
{{ $sect := .sect }}
<ul class="section">
    <li class="section-title">
        <a href="{{ $sect.RelPermalink }}">
            <strong>{{ $sect.Params.chapter }}. {{ $sect.Title }}</strong>
        </a>
    </li>
    {{ range $sect.Pages.ByWeight }}
    <li class="page-item {{ if eq $currentPage . }}active{{ end }}">
        <a href="{{ .RelPermalink }}">
            {{ .Params.chapter }}. {{ .Title }}
        </a>
    </li>
    {{ end }}
    {{ range $sect.Sections.ByWeight }}
        {{ template "section-tree" dict "sect" . "currentpage" $currentPage }}
    {{ end }}
</ul>
{{ end }}
