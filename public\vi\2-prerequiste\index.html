<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chu<PERSON><PERSON> bị môi trường :: AWS System Manager</title>
    <link rel="stylesheet" href="/workshopFCJ/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <nav class="sidebar">
            <div class="logo">
    <a href="https://thuananwork.github.io/workshopFCJ/">
        <h2>AWS System Manager</h2>
    </a>
</div>

<nav class="menu">
    <ul>
        
    </ul>
    
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/1-introduce/">
            <strong>false. Giới thiệu</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/">
            <strong>false. Chuẩn bị môi trường</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/2-prerequiste/2.1-installnodejs/">
            false. Cài đặt NodeJS
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/2-prerequiste/2.2-installyarn/">
            false. Cài đặt Yarn
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/2-prerequiste/2.3-installsamcli/">
            false. Cài đặt SAM CLI
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/2-prerequiste/2.4-createiam/">
            false. Tạo tài khoản và cấu hình IAM
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/2-prerequiste/2.5-create-google-oauth2/">
            false. Tạo Google OAuth2 Project
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/2.1-installnodejs/">
            <strong>false. Cài đặt NodeJS</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/2.2-installyarn/">
            <strong>false. Cài đặt Yarn</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/2.3-installsamcli/">
            <strong>false. Cài đặt SAM CLI</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/2.4-createiam/">
            <strong>false. Tạo tài khoản và cấu hình IAM</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/2.5-create-google-oauth2/">
            <strong>false. Tạo Google OAuth2 Project</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/3-deploybackend/">
            <strong>false. Triển khai backend</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/3-deploybackend/3.1-deploy-backend/">
            false. Triển khai backend bằng SAM CLI/CloudFormation
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/3-deploybackend/3.2-check-status-log-backend/">
            false. Kiểm tra trạng thái và log backend sau khi triển khai
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/3-deploybackend/3.1-deploy-backend/">
            <strong>false. Triển khai backend bằng SAM CLI/CloudFormation</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/3-deploybackend/3.2-check-status-log-backend/">
            <strong>false. Kiểm tra trạng thái và log backend sau khi triển khai</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/4-testbackendapi/">
            <strong>false. Kiểm thử API backend với Postman</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/4-testbackendapi/4.1-endpoint-api-gateway/">
            false. Lấy endpoint API Gateway
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/4-testbackendapi/4.2-get-post-response/">
            false. Gửi request GET/POST để kiểm tra response backend
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/4-testbackendapi/4.1-endpoint-api-gateway/">
            <strong>false. Lấy endpoint API Gateway</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/4-testbackendapi/4.2-get-post-response/">
            <strong>false. Gửi request GET/POST để kiểm tra response backend</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/">
            <strong>false. Triển khai frontend</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.1-frontend-s3/">
            false. Triển khai frontend lên S3 Bucket
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.2-enable-static-hosting/">
            false. Bật static hosting, cấu hình CORS
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.3-s3-bucket-permission/">
            false. Cấp quyền public cho S3 Bucket
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.4-clientid-clientserver/">
            false. Cấu hình Google OAuth2 Client ID và Client Secret
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.5-connect-frontend-api-backend/">
            false. Kết nối frontend với API backend
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.1-frontend-s3/">
            <strong>false. Triển khai frontend lên S3 Bucket</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.2-enable-static-hosting/">
            <strong>false. Bật static hosting, cấu hình CORS</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.3-s3-bucket-permission/">
            <strong>false. Cấp quyền public cho S3 Bucket</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.4-clientid-clientserver/">
            <strong>false. Cấu hình Google OAuth2 Client ID và Client Secret</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.5-connect-frontend-api-backend/">
            <strong>false. Kết nối frontend với API backend</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/6-ssl-s3-static/">
            <strong>false. Thiết lập trang web SSL S3 Static</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/7-demo/">
            <strong>false. Demo và chạy dự án</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/8-cleanup/">
            <strong>false. Dọn dẹp tài nguyên</strong>
        </a>
    </li>
    
    
</ul>

    
</nav>



        </nav>
        <main class="content">
            
<article>
    <header>
        <h1>Chuẩn bị môi trường</h1>
    </header>
    <div class="article-content">
        <p>Trước khi bắt đầu triển khai website thương mại điện tử động sử dụng các dịch vụ AWS như <strong>API Gateway</strong>, <strong>S3</strong>, <strong>Lambda</strong>, <strong>CloudFormation</strong>, <strong>DynamoDB</strong>, <strong>Route 53</strong>, <strong>CloudWatch</strong> và <strong>SAM CLI</strong>, bạn cần hoàn thành một số bước chuẩn bị môi trường cơ bản. Những công cụ và dịch vụ này sẽ giúp bạn triển khai ứng dụng một cách nhanh chóng, an toàn và hiệu quả.</p>
<p>Trong phần này, bạn sẽ thực hiện các bước cài đặt và cấu hình môi trường phát triển cho cả frontend và backend của dự án:</p>
<ul>
<li><strong>Cài đặt NodeJS và Yarn</strong>: Để hỗ trợ việc xây dựng và quản lý frontend.</li>
<li><strong>Cài đặt SAM CLI</strong>: Để triển khai backend serverless trên AWS.</li>
<li><strong>Tạo tài khoản AWS và cấu hình IAM</strong>: Để thiết lập quyền truy cập và bảo mật cho dự án trên AWS.</li>
<li><strong>Cấu hình Google OAuth2 Client ID và Client Secret</strong>: Để tích hợp đăng nhập Google bảo mật cho hệ thống.</li>
</ul>
<p>Các bước chuẩn bị này sẽ đảm bảo rằng bạn có môi trường đầy đủ và hoạt động chính xác trước khi tiến hành triển khai các dịch vụ AWS cho dự án của mình.</p>
<p>⚠️ <strong>Lưu ý</strong>: Hãy chắc chắn rằng bạn đã cài đặt đầy đủ các công cụ trước khi bắt đầu làm việc với các dịch vụ AWS. Nếu không, bạn có thể gặp phải sự cố trong quá trình triển khai.</p>
<h3 id="nội-dung">Nội dung</h3>
<ul>
<li><a href="/workshopFCJ/vi/2-prerequiste/2.1-installnodejs/">Cài đặt NodeJS</a></li>
<li><a href="/workshopFCJ/vi/2-prerequiste/2.2-installyarn/">Cài đặt Yarn cho frontend</a></li>
<li><a href="/workshopFCJ/vi/2-prerequiste/2.3-installsamcli/">Cài đặt SAM CLI cho backend</a></li>
<li><a href="/workshopFCJ/vi/2-prerequiste/2.4-createiam/">Tạo tài khoản &amp; cấu hình IAM</a></li>
<li><a href="/workshopFCJ/vi/2-prerequiste/2.5-create-google-oauth2/">Tạo Google OAuth2 Project</a></li>
</ul>

        
        <ul class="page-list">
            
            <li>
                <a href="/workshopFCJ/vi/2-prerequiste/2.1-installnodejs/">Cài đặt NodeJS</a>
                
                <p><p>NodeJS là một môi trường chạy JavaScript mạnh mẽ và phổ biến, được sử dụng để phát triển các ứng dụng web động. Trong bước này, bạn sẽ cài đặt NodeJS trên các hệ điều hành để phát triển website thương mại điện tử động của mình.</p>
<h4 id="bước-1-cài-đặt-nodejs">Bước 1: Cài đặt NodeJS</h4>
<ol>
<li>
<p><strong>Tải NodeJS từ trang chính:</strong></p>
<ul>
<li>Truy cập trang chính của NodeJS tại <a href="https://nodejs.org/">Node.js</a> và tải phiên bản LTS cho hệ điều hành của bạn (Windows, macOS, hoặc Linux).</li>
<li>Tải bản <strong>.msi</strong> cho Windows, <strong>.pkg</strong> cho macOS hoặc <strong>.tar.xz</strong> cho Linux.</li>
</ul>
</li>
<li>
<p><strong>Cài đặt NodeJS trên Windows:</strong></p></p>
                
            </li>
            
            <li>
                <a href="/workshopFCJ/vi/2-prerequiste/2.2-installyarn/">Cài đặt Yarn</a>
                
                <p><p>Yarn là một trình quản lý gói (package manager) cho JavaScript, được sử dụng rộng rãi trong các dự án frontend để giúp quản lý các thư viện và gói phần mềm một cách hiệu quả. Yarn giúp giải quyết các vấn đề về tốc độ cài đặt, sự nhất quán và bảo mật khi quản lý các thư viện cho dự án.</p>
<h3 id="bước-1-clone-dự-án-về-máy">Bước 1: Clone dự án về máy</h3>
<ol>
<li><strong>Clone mã nguồn dự án từ Github:</strong>
<ul>
<li>Mở terminal và chạy lệnh sau để tải source code về:</li>
</ul>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-sh" data-lang="sh"><span style="display:flex;"><span>git clone https://github.com/thuan120710/Fcjwebfashionthuan_2025
</span></span></code></pre></div></li>
</ol>
<h3 id="bước-2-cài-đặt-yarn">Bước 2: Cài đặt Yarn</h3>
<h4 id="cách-1-cài-đặt-yarn-tự-động-thông-qua-npm">Cách 1: Cài đặt Yarn tự động thông qua npm</h4>
<ol>
<li><strong>Di chuyển vào thư mục frontend của dự án:</strong>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-sh" data-lang="sh"><span style="display:flex;"><span>cd Fcjwebfashionthuan_2025/frontend
</span></span></code></pre></div></li>
<li><strong>Cài đặt các dependency (Yarn sẽ tự động được sử dụng nếu dự án có file yarn.lock):</strong>
<ul>
<li>Chạy lệnh:</li>
</ul>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-sh" data-lang="sh"><span style="display:flex;"><span>npm install
</span></span></code></pre></div><ul>
<li>Nếu dự án có file <code>yarn.lock</code>, nên dùng:</li>
</ul>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-sh" data-lang="sh"><span style="display:flex;"><span>yarn install
</span></span></code></pre></div><ul>
<li>Lệnh này sẽ cài đặt đầy đủ các thư viện cần thiết cho frontend dự án, bao gồm cả Yarn nếu có trong dependency.</li>
</ul>
</li>
</ol>
<h4 id="cách-2-cài-đặt-yarn-thủ-công-nếu-cần">Cách 2: Cài đặt Yarn thủ công (nếu cần)</h4>
<p>Nếu máy tính bạn chưa có Yarn hoặc muốn cài đặt mới hoàn toàn, làm theo hướng dẫn sau:</p></p>
                
            </li>
            
            <li>
                <a href="/workshopFCJ/vi/2-prerequiste/2.3-installsamcli/">Cài đặt SAM CLI</a>
                
                <p><p>AWS SAM CLI là một công cụ dòng lệnh giúp bạn phát triển, đóng gói backend và triển khai các ứng dụng serverless sử dụng AWS Lambda, API Gateway và các dịch vụ AWS serverless khác. Trong bước này, bạn sẽ cài đặt SAM CLI để phát triển backend serverless cho website thương mại điện tử động của mình.</p>
<h4 id="bước-1-cài-đặt-sam-cli">Bước 1: Cài đặt SAM CLI</h4>
<ol>
<li><strong>Cài đặt trên Windows:</strong>
<ul>
<li>Truy cập vào trang <a href="https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/install-sam-cli.html">Installing the AWS SAM CLI</a> để tải bản cài đặt SAM CLI cho Windows.</li>
<li>Click chọn Window</li>
<li>Chạy tệp cài đặt .msi và làm theo các hướng dẫn để hoàn tất quá trình cài đặt.</li>
<li>Mở Termiral gõ <code>sam-version</code></li>
</ul>
</li>
</ol>
<p><img src="/images/install_samcli.png" alt="Error image"></p></p>
                
            </li>
            
            <li>
                <a href="/workshopFCJ/vi/2-prerequiste/2.4-createiam/">Tạo tài khoản và cấu hình IAM</a>
                
                <p><h4 id="tạo-tài-khoản-và-cấu-hình-iam">Tạo tài khoản và cấu hình IAM</h4>
<p>AWS Identity and Access Management (IAM) cho phép bạn quản lý quyền truy cập đến tài nguyên AWS một cách an toàn. Trong bước này, bạn sẽ tạo một IAM user, cấp quyền truy cập thích hợp và cấu hình Access Key để sử dụng trong các bước tiếp theo của workshop.</p>
<h4 id="tạo-iam-user">Tạo IAM User</h4>
<ol>
<li>Truy cập trang <a href="https://aws.amazon.com/console/">AWS Management Console</a> và vào dịch vụ <strong>IAM</strong>.</li>
</ol>
<p><img src="/images/iam_access.png" alt="Error image"></p></p>
                
            </li>
            
            <li>
                <a href="/workshopFCJ/vi/2-prerequiste/2.5-create-google-oauth2/">Tạo Google OAuth2 Project</a>
                
                <p><h4 id="tạo-google-oauth2-project">Tạo Google OAuth2 Project</h4>
<p>Để tích hợp chức năng đăng nhập Google (Google Sign-In) cho website thương mại điện tử, bạn cần đăng ký tạo 1 project Google OAuth2 Project.</p>
<p><strong>Các bước thực hiện:</strong></p>
<ol>
<li>
<p><strong>Truy cập Google Cloud Console</strong></p>
<ul>
<li>Mở <a href="https://console.cloud.google.com/">Google Cloud Console</a>.</li>
<li>Đăng nhập bằng tài khoản Google của bạn.</li>
</ul>
</li>
<li>
<p><strong>Tạo mới một Project (nếu chưa có)</strong></p>
<ul>
<li>
<p>Nhấn <strong>Select a project</strong> &gt; <strong>New Project</strong>.
<img src="/images/create_project_google_auth.png" alt="create_project_google_auth"></p>
</li>
<li>
<p>Đặt tên project, chọn vị trí, nhấn <strong>Create</strong>.
<img src="/images/create_FcjFashionShop_ggouth.png" alt="create_FcjFashionShop_ggouth"></p></p>
                
            </li>
            
        </ul>
        
    </div>
</article>

        </main>
    </div>
</body>
</html>
