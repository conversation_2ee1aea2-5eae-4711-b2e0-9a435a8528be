---
title: "Thiế<PERSON> lập trang web SSL S3 Static"
date: 2023-10-25
weight: 6
chapter: false
pre: "<b>6. </b>"
---

### Thiết lập trang website static có SSL trên S3

Nếu bạn muốn triển khai một **website tĩnh trên S3 có tích hợp SSL (HTTPS)**, bạn không cần viết lại từ đầu.  
Bạn có thể xem **bài hướng dẫn chi tiết, từng bước tại link sau:**

👉 **[Hướng dẫn thiết lập website static có SSL trên S3](https://000082.awsstudygroup.com/vi/)**

Bài viết này sẽ hướng dẫn bạn:

- Tạo và cấu hình S3 Bucket để lưu trữ website tĩnh.
- Thiết lập CloudFront làm CDN để tăng tốc và bảo mật website.
- C<PERSON><PERSON> hình AWS Certificate Manager (ACM) để cấp phát SSL miễn phí cho website.
- <PERSON><PERSON><PERSON> nối domain tùy chỉnh với Route 53.
- Tích hợp đầy đủ HTTPS cho website của bạn một cách dễ dàng.

{{% notice info %}}
**Lưu ý:**  
Không cần thực hiện lại các bước đã trình bày chi tiết trong tài liệu trên, chỉ cần làm theo hướng dẫn là bạn sẽ triển khai được website tĩnh bảo mật với SSL trên AWS!
{{% /notice %}}
