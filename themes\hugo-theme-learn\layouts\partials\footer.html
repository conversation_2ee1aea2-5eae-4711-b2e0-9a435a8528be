        {{ if .Params.chapter }}
            </div> <!-- end chapter-->
        {{ end }}
        </div> 
        {{ partial "custom-comments.html" . }}
      </div>

    <div id="navigation">
        <!-- Next prev page -->
        {{ $currentNode := . }}
        
        {{ template "menu-nextprev" dict "menu" .Site.Home "currentnode" $currentNode }}
        
        {{ define "menu-nextprev" }}
            {{$currentNode := .currentnode }}
            {{ if ne .menu.Params.hidden true}}
                {{if hasPrefix $currentNode.RelPermalink .menu.RelPermalink }}
                    {{ $currentNode.Scratch.Set "NextPageOK" "OK" }}
                    {{ $currentNode.Scratch.Set "prevPage" ($currentNode.Scratch.Get "prevPageTmp") }}
                {{else}}
                    {{if eq ($currentNode.Scratch.Get "NextPageOK") "OK"}}
                        {{ $currentNode.Scratch.Set "NextPageOK" nil }}
                        {{ $currentNode.Scratch.Set "nextPage" .menu }}
                    {{end}}
                {{end}}
                {{ $currentNode.Scratch.Set "prevPageTmp" .menu }}

                    {{ $currentNode.Scratch.Set "pages" .menu.Pages }}
                    {{ if .menu.IsHome}}
                        {{ $currentNode.Scratch.Set "pages" .menu.Sections }}
                    {{ else if .menu.Sections}}
                        {{ $currentNode.Scratch.Set "pages" (.menu.Pages | union .menu.Sections) }}
                    {{end}}
                    {{ $pages := ($currentNode.Scratch.Get "pages") }}

                    {{ range $pages.ByWeight  }}
                        {{ template "menu-nextprev" dict "menu" . "currentnode" $currentNode }}
                    {{end}}
            {{ end }}
        {{ end }}


	 {{$showPrevNext := (and (not .Params.disableNextPrev) (not .Site.Params.disableNextPrev))}}
	 {{if $showPrevNext}}
		{{with ($.Scratch.Get "prevPage")}}
			<a class="nav nav-prev" href="{{.RelPermalink}}" title="{{.Title}}"> <i class="fa fa-chevron-left"></i></a>
		{{end}}
		{{with ($.Scratch.Get "nextPage")}}
			<a class="nav nav-next" href="{{.RelPermalink}}" title="{{.Title}}" style="margin-right: 0px;"><i class="fa fa-chevron-right"></i></a>
		{{end}}
	{{end}}
    </div>

    </section>
    
    <div style="left: -1000px; overflow: scroll; position: absolute; top: -1000px; border: none; box-sizing: content-box; height: 200px; margin: 0px; padding: 0px; width: 200px;">
      <div style="border: none; box-sizing: content-box; height: 200px; margin: 0px; padding: 0px; width: 200px;"></div>
    </div>
    <script src="{{"js/clipboard.min.js" | relURL}}{{ if not .Site.Params.disableAssetsBusting }}?{{ now.Unix }}{{ end }}"></script>
    <script src="{{"js/perfect-scrollbar.min.js" | relURL}}{{ if not .Site.Params.disableAssetsBusting }}?{{ now.Unix }}{{ end }}"></script>
    <script src="{{"js/perfect-scrollbar.jquery.min.js" | relURL}}{{ if not .Site.Params.disableAssetsBusting }}?{{ now.Unix }}{{ end }}"></script>
    <script src="{{"js/jquery.sticky.js" | relURL}}{{ if not .Site.Params.disableAssetsBusting }}?{{ now.Unix }}{{ end }}"></script>
    <script src="{{"js/featherlight.min.js" | relURL}}{{ if not .Site.Params.disableAssetsBusting }}?{{ now.Unix }}{{ end }}"></script>
    <script src="{{"js/highlight.pack.js" | relURL}}{{ if not .Site.Params.disableAssetsBusting }}?{{ now.Unix }}{{ end }}"></script>
    <script>hljs.initHighlightingOnLoad();</script>
    <script src="{{"js/modernizr.custom-3.6.0.js" | relURL}}{{ if not .Site.Params.disableAssetsBusting }}?{{ now.Unix }}{{ end }}"></script>
    <script src="{{"js/learn.js" | relURL}}{{ if not .Site.Params.disableAssetsBusting }}?{{ now.Unix }}{{ end }}"></script>
    <script src="{{"js/hugo-learn.js" | relURL}}{{ if not .Site.Params.disableAssetsBusting }}?{{ now.Unix }}{{ end }}"></script>

    <link href="{{"mermaid/mermaid.css" | relURL}}{{ if not .Site.Params.disableAssetsBusting }}?{{ now.Unix }}{{ end }}" rel="stylesheet" />
    <script src="{{"mermaid/mermaid.js" | relURL}}{{ if not .Site.Params.disableAssetsBusting }}?{{ now.Unix }}{{ end }}"></script>
    <script>
        mermaid.initialize({ startOnLoad: true });
    </script>
    {{ partial "custom-footer.html" . }}
  </body>
</html>
