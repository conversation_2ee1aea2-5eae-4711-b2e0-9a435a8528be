{{ with .Date }}
  <div class="flex align-center text-small book-post-date">
    <img src="{{ "svg/calendar.svg" | relURL }}" class="book-icon " alt="" />
    <span>{{ partial "docs/date" (dict "Date" . "Format" $.Site.Params.BookDateFormat) }}</span>
  </div>
{{ end }}

{{ range $taxonomy, $_ := .Site.Taxonomies }}
  {{ with $terms := $.GetTerms $taxonomy }}
  <div class="text-small">
    {{ range $n, $term := $terms }}{{ if $n }}, {{ end }}
      <a href="{{ $term.RelPermalink }}">{{ $term.Title }}</a>
    {{- end }}
  </div>
  {{ end }}
{{ end }}
