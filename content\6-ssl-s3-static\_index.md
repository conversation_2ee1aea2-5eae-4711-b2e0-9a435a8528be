---
title: "Setup SSL S3 Static Website"
date: 2023-10-25
weight: 6
chapter: false
pre: "<b>6. </b>"
---

### Set Up a Static Website with SSL on S3

If you want to deploy a **static website on S3 with SSL (HTTPS) enabled**, you do not need to rewrite everything from scratch.  
You can refer to the **detailed, step-by-step guide at the following link:**

👉 **[Step-by-step Guide: Setting up a Static Website with SSL on S3](https://000082.awsstudygroup.com/en/)**

This article will guide you through:

- Creating and configuring an S3 Bucket to host your static website.
- Setting up CloudFront as a CDN to accelerate and secure your website.
- Configuring AWS Certificate Manager (ACM) to provision free SSL certificates for your website.
- Connecting your custom domain using Route 53.
- Easily integrating full HTTPS support for your website.

{{% notice info %}}
**Note:**  
You do not need to repeat steps already detailed in the linked guide. Simply follow the instructions to successfully deploy a secure static website with SSL on AWS!
{{% /notice %}}
