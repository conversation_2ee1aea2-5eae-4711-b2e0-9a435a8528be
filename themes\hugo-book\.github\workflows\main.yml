name: Build with <PERSON>

on: [push, pull_request]

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        hugo-version:
          - 'latest'
          - '0.146.7'
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true  # Fetch <PERSON> themes (true OR recursive)
          fetch-depth: 0    # Fetch all history for .GitInfo and .Lastmod

      - name: <PERSON>up <PERSON>
        uses: peaceiris/actions-hugo@v3
        with:
          hugo-version: ${{ matrix.hugo-version }}
          extended: true

      - name: Run Hugo
        working-directory: exampleSite
        run: hugo --themesDir ../..

      # - name: Deploy
      #   uses: peaceiris/actions-gh-pages@v3
      #   with:
      #     github_token: ${{ secrets.GITHUB_TOKEN }}
      #     publish_dir: ./public

