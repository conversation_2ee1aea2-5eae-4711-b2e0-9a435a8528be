<!DOCTYPE html>
<html lang="vi">
<head>
	<meta name="generator" content="Hugo 0.147.8">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Website thương mại điện tử :: AWS System Manager</title>
    <link rel="stylesheet" href="/workshopFCJ/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <nav class="sidebar">
            <div class="logo">
    <a href="https://thuananwork.github.io/workshopFCJ/">
        <h2>AWS System Manager</h2>
    </a>
</div>

<nav class="menu">
    <ul>
        
    </ul>
    
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/1-introduce/">
            <strong>false. Giới thiệu</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/">
            <strong>false. Chuẩn bị môi trường</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/2-prerequiste/2.1-installnodejs/">
            false. Cài đặt NodeJS
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/2-prerequiste/2.2-installyarn/">
            false. Cài đặt Yarn
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/2-prerequiste/2.3-installsamcli/">
            false. Cài đặt SAM CLI
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/2-prerequiste/2.4-createiam/">
            false. Tạo tài khoản và cấu hình IAM
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/2-prerequiste/2.5-create-google-oauth2/">
            false. Tạo Google OAuth2 Project
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/2.1-installnodejs/">
            <strong>false. Cài đặt NodeJS</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/2.2-installyarn/">
            <strong>false. Cài đặt Yarn</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/2.3-installsamcli/">
            <strong>false. Cài đặt SAM CLI</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/2.4-createiam/">
            <strong>false. Tạo tài khoản và cấu hình IAM</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/2.5-create-google-oauth2/">
            <strong>false. Tạo Google OAuth2 Project</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/3-deploybackend/">
            <strong>false. Triển khai backend</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/3-deploybackend/3.1-deploy-backend/">
            false. Triển khai backend bằng SAM CLI/CloudFormation
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/3-deploybackend/3.2-check-status-log-backend/">
            false. Kiểm tra trạng thái và log backend sau khi triển khai
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/3-deploybackend/3.1-deploy-backend/">
            <strong>false. Triển khai backend bằng SAM CLI/CloudFormation</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/3-deploybackend/3.2-check-status-log-backend/">
            <strong>false. Kiểm tra trạng thái và log backend sau khi triển khai</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/4-testbackendapi/">
            <strong>false. Kiểm thử API backend với Postman</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/4-testbackendapi/4.1-endpoint-api-gateway/">
            false. Lấy endpoint API Gateway
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/4-testbackendapi/4.2-get-post-response/">
            false. Gửi request GET/POST để kiểm tra response backend
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/4-testbackendapi/4.1-endpoint-api-gateway/">
            <strong>false. Lấy endpoint API Gateway</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/4-testbackendapi/4.2-get-post-response/">
            <strong>false. Gửi request GET/POST để kiểm tra response backend</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/">
            <strong>false. Triển khai frontend</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.1-frontend-s3/">
            false. Triển khai frontend lên S3 Bucket
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.2-enable-static-hosting/">
            false. Bật static hosting, cấu hình CORS
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.3-s3-bucket-permission/">
            false. Cấp quyền public cho S3 Bucket
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.4-clientid-clientserver/">
            false. Cấu hình Google OAuth2 Client ID và Client Secret
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.5-connect-frontend-api-backend/">
            false. Kết nối frontend với API backend
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.1-frontend-s3/">
            <strong>false. Triển khai frontend lên S3 Bucket</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.2-enable-static-hosting/">
            <strong>false. Bật static hosting, cấu hình CORS</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.3-s3-bucket-permission/">
            <strong>false. Cấp quyền public cho S3 Bucket</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.4-clientid-clientserver/">
            <strong>false. Cấu hình Google OAuth2 Client ID và Client Secret</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.5-connect-frontend-api-backend/">
            <strong>false. Kết nối frontend với API backend</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/6-ssl-s3-static/">
            <strong>false. Thiết lập trang web SSL S3 Static</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/7-demo/">
            <strong>false. Demo và chạy dự án</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/8-cleanup/">
            <strong>false. Dọn dẹp tài nguyên</strong>
        </a>
    </li>
    
    
</ul>

    
</nav>



        </nav>
        <main class="content">
            
<article>
    <header>
        <h1>Website thương mại điện tử</h1>
    </header>
    <div class="article-content">
        <h1 id="xây-dựng-website-thương-mại-điện-tử-động-với-aws-serverless">Xây dựng website thương mại điện tử động với AWS Serverless</h1>
<h3 id="tổng-quan">Tổng quan</h3>
<ul>
<li>Trong Workshop này, bạn sẽ triển khai một <strong>website thương mại điện tử động</strong> sử dụng framework Hugo trên nền tảng điện toán đám mây AWS. Bạn sẽ học cách chuẩn bị môi trường, cấu hình tài khoản AWS, xây dựng website với Hugo, và triển khai toàn bộ hệ thống trên các dịch vụ chính của AWS như API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch, đồng thời sử dụng SAM CLI để tự động hoá quá trình triển khai.</li>
</ul>
<p>Ngoài ra, bạn sẽ được hướng dẫn <strong>thiết lập một trang website tĩnh trên S3 với SSL (HTTPS) đầy đủ</strong> để tăng bảo mật, giúp website phục vụ tốt hơn cho khách hàng toàn cầu.</p>
<p><img src="/images/fcjfashionshop_drawio.png" alt="Kiến trúc tổng thể"></p>
<h3 id="mục-tiêu-của-workshop">Mục tiêu của Workshop</h3>
<ul>
<li>Hiểu và thao tác thành thạo các công cụ AWS cần thiết cho một dự án website động.</li>
<li>Biết cách chuẩn bị, cài đặt, cấu hình môi trường phát triển cho dự án Hugo.</li>
<li>Xây dựng, đóng gói và triển khai website động với Hugo kết hợp các dịch vụ serverless hiện đại của AWS.</li>
<li>Thiết kế và triển khai (deploy) với API Gateway, xử lý logic động bằng Lambda, lưu trữ dữ liệu trên DynamoDB, và quản lý website động trên S3.</li>
<li>Sử dụng CloudFormation để tự động tạo và cấu hình tài nguyên AWS, theo dõi và giám sát hoạt động hệ thống với CloudWatch.</li>
<li><strong>Thiết lập website tĩnh trên S3 có tích hợp SSL (HTTPS) với AWS Certificate Manager và CloudFront.</strong></li>
<li>Cấu hình tên miền và phân giải DNS với Route 53 để truy cập website động qua internet.</li>
<li>Ứng dụng quy trình DevOps tự động hóa triển khai và vận hành website trên nền tảng AWS một cách hiệu quả.</li>
</ul>
<h3 id="kiến-thức-thu-được-sau-workshop">Kiến thức thu được sau Workshop</h3>
<p>Sau khi hoàn thành Workshop này, bạn sẽ:</p>
<ul>
<li>Hiểu rõ kiến trúc và quy trình triển khai một website thương mại điện tử động trên nền tảng AWS.</li>
<li>Học được cách sử dụng các dịch vụ AWS như API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch và SAM CLI trong một dự án thực tế.</li>
<li>Biết cách xây dựng, đóng gói và triển khai website động với Hugo, kết nối frontend với backend qua API Gateway và Lambda.</li>
<li><strong>Biết cách thiết lập website tĩnh trên S3, sử dụng CloudFront và AWS Certificate Manager để kích hoạt HTTPS cho website.</strong></li>
<li>Thực hành quản lý dữ liệu động với DynamoDB, quản lý hạ tầng tự động bằng CloudFormation.</li>
<li>Cấu hình tên miền với Route 53, giám sát hệ thống và log ứng dụng qua CloudWatch.</li>
<li>Sẵn sàng áp dụng kiến thức vào các dự án thực tế, các bài toán về website động, serverless, hoặc DevOps trên AWS.</li>
</ul>
<h3 id="nội-dung">Nội dung</h3>
<ol>
<li><a href="/workshopFCJ/vi/1-introduce/">Giới thiệu</a></li>
<li><a href="/workshopFCJ/vi/2-prerequiste/">Các bước chuẩn bị</a></li>
<li><a href="/workshopFCJ/vi/3-deploybackend/">Triển khai backend</a></li>
<li><a href="/workshopFCJ/vi/4-testbackendapi/">Kiểm thử API backend với Postman</a></li>
<li><a href="/workshopFCJ/vi/5-deployfrontend/">Triển khai frontend</a></li>
<li><a href="/workshopFCJ/vi/6-ssl-s3-static/">Thiết lập trang web SSL S3 Static</a></li>
<li><a href="/workshopFCJ/vi/7-demo/">Demo và chạy thử dự án</a></li>
<li><a href="/workshopFCJ/vi/8-cleanup/">Dọn dẹp tài nguyên</a></li>
</ol>

    </div>
</article>

        </main>
    </div>
</body>
</html>
