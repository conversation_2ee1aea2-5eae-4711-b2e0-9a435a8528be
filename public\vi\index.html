<!DOCTYPE html>
<html lang="vi" class="js csstransforms3d">
  <head><script src="/workshopFCJ/livereload.js?mindelay=10&amp;v=2&amp;port=1313&amp;path=workshopFCJ/livereload" data-no-instant defer></script>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="generator" content="Hugo 0.147.8">
    <meta name="description" content="">
<meta name="author" content="<EMAIL>">

    <link rel="icon" href="/workshopFCJ/images/favicon.png" type="image/png">

    <title>Website thương mại điện tử :: AWS System Manager</title>

    
    <link href="/workshopFCJ/css/nucleus.css?1751833543" rel="stylesheet">
    <link href="/workshopFCJ/css/fontawesome-all.min.css?1751833543" rel="stylesheet">
    <link href="/workshopFCJ/css/hybrid.css?1751833543" rel="stylesheet">
    <link href="/workshopFCJ/css/featherlight.min.css?1751833543" rel="stylesheet">
    <link href="/workshopFCJ/css/perfect-scrollbar.min.css?1751833543" rel="stylesheet">
    <link href="/workshopFCJ/css/auto-complete.css?1751833543" rel="stylesheet">
    <link href="/workshopFCJ/css/atom-one-dark-reasonable.css?1751833543" rel="stylesheet">
    <link href="/workshopFCJ/css/theme.css?1751833543" rel="stylesheet">
    <link href="/workshopFCJ/css/hugo-theme.css?1751833543" rel="stylesheet">
    
    <link href="/workshopFCJ/css/theme-workshop.css?1751833543" rel="stylesheet">
    
    

    <script src="/workshopFCJ/js/jquery-3.3.1.min.js?1751833543"></script>

    <style>
      :root #header + #content > #left > #rlblock_left{
          display:none !important;
      }
      
    </style>
    
  </head>
  <body class="" data-url="/workshopFCJ/vi/">
    <nav id="sidebar" class="showVisitedLinks">

  
  
  <div id="header-wrapper">
    <div id="header">
      <a id="logo" href="/">

<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 30" width="30%"><defs><style>.cls-1{fill:#fff;}.cls-2{fill:#f90;fill-rule:evenodd;}</style></defs><title>AWS-Logo_White-Color</title><path class="cls-1" d="M14.09,10.85a4.7,4.7,0,0,0,.19,1.48,7.73,7.73,0,0,0,.54,**********,0,0,1,.***********,0,0,1-.32.49l-1,.7a.83.83,0,0,1-.***********,0,0,1-.49-.23,3.8,3.8,0,0,1-.6-.77q-.25-.42-.51-1a6.14,6.14,0,0,1-4.89,2.3,4.54,4.54,0,0,1-3.32-1.19,4.27,4.27,0,0,1-1.22-3.2A4.28,4.28,0,0,1,3.61,7.75,6.06,6.06,0,0,1,7.69,6.46a12.47,12.47,0,0,1,1.76.13q.92.13,1.91.36V5.73a3.65,3.65,0,0,0-.79-2.66A3.81,3.81,0,0,0,7.86,2.3a7.71,7.71,0,0,0-1.79.22,12.78,12.78,0,0,0-1.79.57,4.55,4.55,0,0,1-.58.22l-.26,0q-.35,0-.35-.52V2a1.09,1.09,0,0,1,.12-.58,1.2,1.2,0,0,1,.47-.35A10.88,10.88,0,0,1,5.77.32,10.19,10.19,0,0,1,8.36,0a6,6,0,0,1,4.35,1.35,5.49,5.49,0,0,1,1.38,4.09ZM7.34,13.38a5.36,5.36,0,0,0,1.72-.31A3.63,3.63,0,0,0,10.63,12,2.62,2.62,0,0,0,11.19,11a5.63,5.63,0,0,0,.16-1.44v-.7a14.35,14.35,0,0,0-1.53-.28,12.37,12.37,0,0,0-1.56-.1,3.84,3.84,0,0,0-2.47.67A2.34,2.34,0,0,0,5,11a2.35,2.35,0,0,0,.61,1.76A2.4,2.4,0,0,0,7.34,13.38Zm13.35,1.8a1,1,0,0,1-.64-.16,1.3,1.3,0,0,1-.35-.65L15.81,1.51a3,3,0,0,1-.15-.67.36.36,0,0,1,.41-.41H17.7a1,1,0,0,1,.65.16,1.4,1.4,0,0,1,.33.65l2.79,11,2.59-11A1.17,1.17,0,0,1,24.39.6a1.1,1.1,0,0,1,.67-.16H26.4a1.1,1.1,0,0,1,.67.16,1.17,1.17,0,0,1,.32.65L30,12.39,32.88,1.25A1.39,1.39,0,0,1,33.22.6a1,1,0,0,1,.65-.16h1.54a.36.36,0,0,1,.41.41,1.36,1.36,0,0,1,0,.26,3.64,3.64,0,0,1-.12.41l-4,12.86a1.3,1.3,0,0,1-.35.65,1,1,0,0,1-.64.16H29.25a1,1,0,0,1-.67-.17,1.26,1.26,0,0,1-.32-.67L25.67,3.64,23.11,14.34a1.26,1.26,0,0,1-.32.67,1,1,0,0,1-.67.17Zm21.36.44a11.28,11.28,0,0,1-2.56-.29,7.44,7.44,0,0,1-1.92-.67,1,1,0,0,1-.61-.93v-.84q0-.52.38-.52a.9.9,0,0,1,.31.06l.42.17a8.77,8.77,0,0,0,1.83.58,9.78,9.78,0,0,0,2,.2,4.48,4.48,0,0,0,2.43-.55,1.76,1.76,0,0,0,.86-1.57,1.61,1.61,0,0,0-.45-1.16A4.29,4.29,0,0,0,43,9.22l-2.41-.76A5.15,5.15,0,0,1,38,6.78a3.94,3.94,0,0,1-.83-2.41,3.7,3.7,0,0,1,.45-1.85,4.47,4.47,0,0,1,1.19-1.37A5.27,5.27,0,0,1,40.51.29,7.4,7.4,0,0,1,42.6,0a8.87,8.87,0,0,1,1.12.07q.57.07,1.08.19t.95.26a4.27,4.27,0,0,1,.7.29,1.59,1.59,0,0,1,.49.41.94.94,0,0,1,.15.55v.79q0,.52-.38.52a1.76,1.76,0,0,1-.64-.2,7.74,7.74,0,0,0-3.2-.64,4.37,4.37,0,0,0-2.21.47,1.6,1.6,0,0,0-.79,1.48,1.58,1.58,0,0,0,.49,1.18,4.94,4.94,0,0,0,1.83.92L44.55,7a5.08,5.08,0,0,1,2.57,1.6A3.76,3.76,0,0,1,47.9,11a4.21,4.21,0,0,1-.44,1.93,4.4,4.4,0,0,1-1.21,1.47,5.43,5.43,0,0,1-1.85.93A8.25,8.25,0,0,1,42.05,15.62Z"></path><path class="cls-2" d="M45.19,23.81C39.72,27.85,31.78,30,25,30A36.64,36.64,0,0,1,.22,20.57c-.51-.46-.06-1.09.56-.74A49.78,49.78,0,0,0,25.53,26.4,49.23,49.23,0,0,0,44.4,22.53C45.32,22.14,46.1,23.14,45.19,23.81Z"></path><path class="cls-2" d="M47.47,21.21c-.7-.9-4.63-.42-6.39-.21-.53.06-.62-.4-.14-.74,3.13-2.2,8.27-1.57,8.86-.83s-.16,5.89-3.09,8.35c-.45.38-.88.18-.68-.32C46.69,25.8,48.17,22.11,47.47,21.21Z"></path></svg>

</a>

    </div>
    
    <div class="searchbox">
    <label for="search-by"><i class="fas fa-search"></i></label>
    <input data-search-input id="search-by" type="search" placeholder="">
    <span data-search-clear=""><i class="fas fa-times"></i></span>
</div>

<script type="text/javascript" src="/workshopFCJ/%20js/lunr.min.js?1751833543"></script>
<script type="text/javascript" src="/workshopFCJ/%20js/auto-complete.js?1751833543"></script>
<script type="text/javascript">
    { { if hugo.IsMultilingual } }
    var baseurl = "http:\/\/localhost:1313\/workshopFCJ\/\/vi";
    { { else } }
    var baseurl = "http:\/\/localhost:1313\/workshopFCJ\/";
    { { end } }
</script>
<script type="text/javascript" src="/workshopFCJ/%20js/search.js?1751833543"></script>
    
  </div>

  <div class="highlightable">
    <ul class="topics">

      
      
      







<li data-nav-id="/workshopFCJ/vi/1-introduce/" title="Giới thiệu" class="dd-item 
        
        
        
        ">
  <a href="/workshopFCJ/vi/1-introduce/">
     <b> 1. </b> Giới thiệu
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



      
      







<li data-nav-id="/workshopFCJ/vi/2-prerequiste/" title="Chuẩn bị môi trường" class="dd-item 
        
        
        
        ">
  <a href="/workshopFCJ/vi/2-prerequiste/">
     <b> 2 </b> Chuẩn bị môi trường
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
  <ul>
    
    
    
    
    

    
    
    
    







<li data-nav-id="/workshopFCJ/vi/2-prerequiste/2.1-installnodejs/" title="Cài đặt NodeJS" class="dd-item 
        
        
        
        ">
  <a href="/workshopFCJ/vi/2-prerequiste/2.1-installnodejs/">
     <b> 2.1 </b> Cài đặt NodeJS
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/workshopFCJ/vi/2-prerequiste/2.2-installyarn/" title="Cài đặt Yarn" class="dd-item 
        
        
        
        ">
  <a href="/workshopFCJ/vi/2-prerequiste/2.2-installyarn/">
     <b> 2.2 </b> Cài đặt Yarn
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/workshopFCJ/vi/2-prerequiste/2.3-installsamcli/" title="Cài đặt SAM CLI" class="dd-item 
        
        
        
        ">
  <a href="/workshopFCJ/vi/2-prerequiste/2.3-installsamcli/">
     <b> 2.3 </b> Cài đặt SAM CLI
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/workshopFCJ/vi/2-prerequiste/2.4-createiam/" title="Tạo tài khoản và cấu hình IAM" class="dd-item 
        
        
        
        ">
  <a href="/workshopFCJ/vi/2-prerequiste/2.4-createiam/">
     <b> 2.4 </b> Tạo tài khoản và cấu hình IAM
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/workshopFCJ/vi/2-prerequiste/2.5-create-google-oauth2/" title="Tạo Google OAuth2 Project" class="dd-item 
        
        
        
        ">
  <a href="/workshopFCJ/vi/2-prerequiste/2.5-create-google-oauth2/">
     <b> 2.5 </b> Tạo Google OAuth2 Project
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
  </ul>
  
</li>



      
      







<li data-nav-id="/workshopFCJ/vi/3-deploybackend/" title="Triển khai backend" class="dd-item 
        
        
        
        ">
  <a href="/workshopFCJ/vi/3-deploybackend/">
     <b> 3. </b> Triển khai backend
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
  <ul>
    
    
    
    
    

    
    
    
    







<li data-nav-id="/workshopFCJ/vi/3-deploybackend/3.1-deploy-backend/" title="Triển khai backend bằng SAM CLI/CloudFormation" class="dd-item 
        
        
        
        ">
  <a href="/workshopFCJ/vi/3-deploybackend/3.1-deploy-backend/">
     <b> 3.1 </b> Triển khai backend bằng SAM CLI/CloudFormation
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/workshopFCJ/vi/3-deploybackend/3.2-check-status-log-backend/" title="Kiểm tra trạng thái và log backend sau khi triển khai" class="dd-item 
        
        
        
        ">
  <a href="/workshopFCJ/vi/3-deploybackend/3.2-check-status-log-backend/">
     <b> 3.2 </b> Kiểm tra trạng thái và log backend sau khi triển khai
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
  </ul>
  
</li>



      
      







<li data-nav-id="/workshopFCJ/vi/4-testbackendapi/" title="Kiểm thử API backend với Postman" class="dd-item 
        
        
        
        ">
  <a href="/workshopFCJ/vi/4-testbackendapi/">
     <b> 4 </b> Kiểm thử API backend với Postman
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
  <ul>
    
    
    
    
    

    
    
    
    







<li data-nav-id="/workshopFCJ/vi/4-testbackendapi/4.1-endpoint-api-gateway/" title="Lấy endpoint API Gateway" class="dd-item 
        
        
        
        ">
  <a href="/workshopFCJ/vi/4-testbackendapi/4.1-endpoint-api-gateway/">
     <b> 4.1 </b> Lấy endpoint API Gateway
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/workshopFCJ/vi/4-testbackendapi/4.2-get-post-response/" title="Gửi request GET/POST để kiểm tra response backend" class="dd-item 
        
        
        
        ">
  <a href="/workshopFCJ/vi/4-testbackendapi/4.2-get-post-response/">
     <b> 4.2 </b> Gửi request GET/POST để kiểm tra response backend
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
  </ul>
  
</li>



      
      







<li data-nav-id="/workshopFCJ/vi/5-deployfrontend/" title="Triển khai frontend" class="dd-item 
        
        
        
        ">
  <a href="/workshopFCJ/vi/5-deployfrontend/">
     <b> 5 </b> Triển khai frontend
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
  <ul>
    
    
    
    
    

    
    
    
    







<li data-nav-id="/workshopFCJ/vi/5-deployfrontend/5.1-frontend-s3/" title="Triển khai frontend lên S3 Bucket" class="dd-item 
        
        
        
        ">
  <a href="/workshopFCJ/vi/5-deployfrontend/5.1-frontend-s3/">
     <b> 5.1 </b> Triển khai frontend lên S3 Bucket
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/workshopFCJ/vi/5-deployfrontend/5.2-enable-static-hosting/" title="Bật static hosting, cấu hình CORS" class="dd-item 
        
        
        
        ">
  <a href="/workshopFCJ/vi/5-deployfrontend/5.2-enable-static-hosting/">
     <b> 5.2 </b> Bật static hosting, cấu hình CORS
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/workshopFCJ/vi/5-deployfrontend/5.3-s3-bucket-permission/" title="Cấp quyền public cho S3 Bucket" class="dd-item 
        
        
        
        ">
  <a href="/workshopFCJ/vi/5-deployfrontend/5.3-s3-bucket-permission/">
     <b> 5.3 </b> Cấp quyền public cho S3 Bucket
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/workshopFCJ/vi/5-deployfrontend/5.4-clientid-clientserver/" title="Cấu hình Google OAuth2 Client ID và Client Secret" class="dd-item 
        
        
        
        ">
  <a href="/workshopFCJ/vi/5-deployfrontend/5.4-clientid-clientserver/">
     <b> 5.4 </b> Cấu hình Google OAuth2 Client ID và Client Secret
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/workshopFCJ/vi/5-deployfrontend/5.5-connect-frontend-api-backend/" title="Kết nối frontend với API backend" class="dd-item 
        
        
        
        ">
  <a href="/workshopFCJ/vi/5-deployfrontend/5.5-connect-frontend-api-backend/">
     <b> 5.5 </b> Kết nối frontend với API backend
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
  </ul>
  
</li>



      
      







<li data-nav-id="/workshopFCJ/vi/6-ssl-s3-static/" title="Thiết lập trang web SSL S3 Static" class="dd-item 
        
        
        
        ">
  <a href="/workshopFCJ/vi/6-ssl-s3-static/">
    <b>6. </b>Thiết lập trang web SSL S3 Static
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



      
      







<li data-nav-id="/workshopFCJ/vi/7-demo/" title="Demo và chạy dự án" class="dd-item 
        
        
        
        ">
  <a href="/workshopFCJ/vi/7-demo/">
    <b>7. </b>Demo và chạy dự án
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



      
      







<li data-nav-id="/workshopFCJ/vi/8-cleanup/" title="Dọn dẹp tài nguyên" class="dd-item 
        
        
        
        ">
  <a href="/workshopFCJ/vi/8-cleanup/">
    <b>8. </b>Dọn dẹp tài nguyên
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



      
      
    </ul>

    
    
    <section id="shortcuts">
      <h3>More</h3>
      <ul>
        
        <li>
          <a class="padding" href="https://www.facebook.com/groups/awsstudygroupfcj/"><i class='fab fa-facebook'></i> AWS Study Group</a>
        </li>
        
      </ul>
    </section>
    

    
    <section id="prefooter">
      <hr />
      <ul>
        
        <li>
          <a class="padding">
            <i class="fas fa-language fa-fw"></i>
            <div class="select-style">
              <select id="select-language" onchange="location = this.value;">
                
                
                
                
                
                
                
                
                <option id="en" value="http://localhost:1313/workshopFCJ/">English
                </option>
                
                
                
                
                
                
                
                
                
                
                
                
                
                <option id="vi" value="http://localhost:1313/workshopFCJ/vi/" selected>Tiếng Việt</option>
                
                
                
                
              </select>
              <svg version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="255px" height="255px"
                viewBox="0 0 255 255" style="enable-background:new 0 0 255 255;" xml:space="preserve">
                <g>
                  <g id="arrow-drop-down">
                    <polygon points="0,63.75 127.5,191.25 255,63.75 		" />
                  </g>
                </g>
              </svg>
            </div>
          </a>
        </li>
        

        
        <li><a class="padding" href="#" data-clear-history-toggle=""><i class="fas fa-history fa-fw"></i> Clear History</a></li>
        
      </ul>
    </section>
    
    <section id="footer">
      <left>
    
     <b> Workshop</b> <br>
    <img src="https://hitwebcounter.com/counter/counter.php?page=7920860&style=0038&nbdigits=9&type=page&initCount=0" title="Migrate" Alt="web counter"   border="0" /></a>  <br>
     <b> <a href="https://cloudjourney.awsstudygroup.com/">Cloud Journey</a></b> <br>
    <img src="https://hitwebcounter.com/counter/counter.php?page=7830807&style=0038&nbdigits=9&type=page&initCount=0" title="Total CLoud Journey" Alt="web counter"   border="0"   />
     
</left>
<left>
    <br>
    <br>
        <b> Last Updated </b> <br>
        <i><font color=orange>30-01-2022</font></i>
    </left>
    <left>
        <br>
        <br>
            <b> Team </b> <br>
           
            <i> <a href="https://www.linkedin.com/in/sutrinh/"  style="color:orange">Sử Trịnh  </a> <br>
                <a href="https://www.linkedin.com/in/jotaguy"  style="color:orange">Gia Hưng </a> <br>
                <a href="https://www.linkedin.com/in/hiepnguyendt"  style="color:orange">Thanh Hiệp </a>
               
        </i>
        </left>

<script async defer src="https://buttons.github.io/buttons.js"></script>

    </section>
  </div>
</nav>



        <section id="body">
        <div id="overlay"></div>
        <div class="padding highlightable">
              
        <div id="head-tags">
        
        </div>
        
        <div id="body-inner">
          

        

<span id="sidebar-toggle-span">
<a href="#" id="sidebar-toggle" data-sidebar-toggle=""><i class="fas fa-bars"></i> navigation</a>
</span>

 
<h1 id="xây-dựng-website-thương-mại-điện-tử-động-với-aws-serverless">Xây dựng website thương mại điện tử động với AWS Serverless</h1>
<h3 id="tổng-quan">Tổng quan</h3>
<ul>
<li>Trong Workshop này, bạn sẽ triển khai một <strong>website thương mại điện tử động</strong> sử dụng framework Hugo trên nền tảng điện toán đám mây AWS. Bạn sẽ học cách chuẩn bị môi trường, cấu hình tài khoản AWS, xây dựng website với Hugo, và triển khai toàn bộ hệ thống trên các dịch vụ chính của AWS như API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch, đồng thời sử dụng SAM CLI để tự động hoá quá trình triển khai.</li>
</ul>
<p>Ngoài ra, bạn sẽ được hướng dẫn <strong>thiết lập một trang website tĩnh trên S3 với SSL (HTTPS) đầy đủ</strong> để tăng bảo mật, giúp website phục vụ tốt hơn cho khách hàng toàn cầu.</p>
<p><img src="/images/fcjfashionshop_drawio.png" alt="Kiến trúc tổng thể"></p>
<h3 id="mục-tiêu-của-workshop">Mục tiêu của Workshop</h3>
<ul>
<li>Hiểu và thao tác thành thạo các công cụ AWS cần thiết cho một dự án website động.</li>
<li>Biết cách chuẩn bị, cài đặt, cấu hình môi trường phát triển cho dự án Hugo.</li>
<li>Xây dựng, đóng gói và triển khai website động với Hugo kết hợp các dịch vụ serverless hiện đại của AWS.</li>
<li>Thiết kế và triển khai (deploy) với API Gateway, xử lý logic động bằng Lambda, lưu trữ dữ liệu trên DynamoDB, và quản lý website động trên S3.</li>
<li>Sử dụng CloudFormation để tự động tạo và cấu hình tài nguyên AWS, theo dõi và giám sát hoạt động hệ thống với CloudWatch.</li>
<li><strong>Thiết lập website tĩnh trên S3 có tích hợp SSL (HTTPS) với AWS Certificate Manager và CloudFront.</strong></li>
<li>Cấu hình tên miền và phân giải DNS với Route 53 để truy cập website động qua internet.</li>
<li>Ứng dụng quy trình DevOps tự động hóa triển khai và vận hành website trên nền tảng AWS một cách hiệu quả.</li>
</ul>
<h3 id="kiến-thức-thu-được-sau-workshop">Kiến thức thu được sau Workshop</h3>
<p>Sau khi hoàn thành Workshop này, bạn sẽ:</p>
<ul>
<li>Hiểu rõ kiến trúc và quy trình triển khai một website thương mại điện tử động trên nền tảng AWS.</li>
<li>Học được cách sử dụng các dịch vụ AWS như API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch và SAM CLI trong một dự án thực tế.</li>
<li>Biết cách xây dựng, đóng gói và triển khai website động với Hugo, kết nối frontend với backend qua API Gateway và Lambda.</li>
<li><strong>Biết cách thiết lập website tĩnh trên S3, sử dụng CloudFront và AWS Certificate Manager để kích hoạt HTTPS cho website.</strong></li>
<li>Thực hành quản lý dữ liệu động với DynamoDB, quản lý hạ tầng tự động bằng CloudFormation.</li>
<li>Cấu hình tên miền với Route 53, giám sát hệ thống và log ứng dụng qua CloudWatch.</li>
<li>Sẵn sàng áp dụng kiến thức vào các dự án thực tế, các bài toán về website động, serverless, hoặc DevOps trên AWS.</li>
</ul>
<h3 id="nội-dung">Nội dung</h3>
<ol>
<li><a href="/workshopFCJ/vi/1-introduce/">Giới thiệu</a></li>
<li><a href="/workshopFCJ/vi/2-prerequiste/">Các bước chuẩn bị</a></li>
<li><a href="/workshopFCJ/vi/3-deploybackend/">Triển khai backend</a></li>
<li><a href="/workshopFCJ/vi/4-testbackendapi/">Kiểm thử API backend với Postman</a></li>
<li><a href="/workshopFCJ/vi/5-deployfrontend/">Triển khai frontend</a></li>
<li><a href="/workshopFCJ/vi/6-ssl-s3-static/">Thiết lập trang web SSL S3 Static</a></li>
<li><a href="/workshopFCJ/vi/7-demo/">Demo và chạy thử dự án</a></li>
<li><a href="/workshopFCJ/vi/8-cleanup/">Dọn dẹp tài nguyên</a></li>
</ol>
	
  
        
        </div> 
        

      </div>

    <div id="navigation">
        
        
        
        
            
            
                
                    
                    
                
                

                    
                    
                        
                    
                    

                    
                        
            
            
                
                    
                        
                        
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                        
                    
                    

                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                        
                    
                    

                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                        
                    
                    

                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                        
                    
                    

                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
            
        
        
        


	 
	 
		
		
			<a class="nav nav-next" href="/workshopFCJ/vi/1-introduce/" title="Giới thiệu" style="margin-right: 0px;"><i class="fa fa-chevron-right"></i></a>
		
	
    </div>

    </section>
    
    <div style="left: -1000px; overflow: scroll; position: absolute; top: -1000px; border: none; box-sizing: content-box; height: 200px; margin: 0px; padding: 0px; width: 200px;">
      <div style="border: none; box-sizing: content-box; height: 200px; margin: 0px; padding: 0px; width: 200px;"></div>
    </div>
    <script src="/workshopFCJ/js/clipboard.min.js?1751833543"></script>
    <script src="/workshopFCJ/js/perfect-scrollbar.min.js?1751833543"></script>
    <script src="/workshopFCJ/js/perfect-scrollbar.jquery.min.js?1751833543"></script>
    <script src="/workshopFCJ/js/jquery.sticky.js?1751833543"></script>
    <script src="/workshopFCJ/js/featherlight.min.js?1751833543"></script>
    <script src="/workshopFCJ/js/highlight.pack.js?1751833543"></script>
    <script>hljs.initHighlightingOnLoad();</script>
    <script src="/workshopFCJ/js/modernizr.custom-3.6.0.js?1751833543"></script>
    <script src="/workshopFCJ/js/learn.js?1751833543"></script>
    <script src="/workshopFCJ/js/hugo-learn.js?1751833543"></script>

    <link href="/workshopFCJ/mermaid/mermaid.css?1751833543" rel="stylesheet" />
    <script src="/workshopFCJ/mermaid/mermaid.js?1751833543"></script>
    <script>
        mermaid.initialize({ startOnLoad: true });
    </script>
    <script>
  (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
  (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
  m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
  })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

  ga('create', 'UA-158079754-2', 'auto');
  ga('send', 'pageview');

</script>
  </body>
</html>

