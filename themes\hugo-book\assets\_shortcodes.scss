.markdown {
  // {{< expand "Label" "icon" >}}
  .book-expand {
    margin-top: $padding-16;
    margin-bottom: $padding-16;

    border: $padding-1 solid var(--gray-200);
    border-radius: $border-radius;

    overflow: hidden;

    .book-expand-head {
      background: var(--gray-100);
      padding: $padding-8 $padding-16;
      cursor: pointer;
    }

    .book-expand-content {
      display: none;
      padding: $padding-16;
    }

    input[type="checkbox"]:checked + .book-expand-content {
      display: block;
    }
  }

  // {{< tabs >}}
  .book-tabs {
    margin-top: $padding-16;
    margin-bottom: $padding-16;

    border: $padding-1 solid var(--gray-200);
    border-radius: $border-radius;

    overflow: hidden;

    display: flex;
    flex-wrap: wrap;

    label {
      display: inline-block;
      padding: $padding-8 $padding-16;
      border-bottom: $padding-1 transparent;
      cursor: pointer;
    }

    .book-tabs-content {
      order: 999; //Move content blocks to the end
      width: 100%;
      border-top: $padding-1 solid var(--gray-100);
      padding: $padding-16;
      display: none;
    }

    input[type="radio"]:checked + label {
      border-bottom: $padding-1 solid var(--color-link);
    }
    input[type="radio"]:checked + label + .book-tabs-content {
      display: block;
    }
    input[type="radio"]:focus + label {
      @include outline;
    }
  }

  // {{< columns >}}
  .book-columns {
    margin-left: -$padding-16;
    margin-right: -$padding-16;

    > div {
      margin: $padding-16 0;
      min-width: $body-min-width * 0.66;
      padding: 0 $padding-16;
    }
  }

  // {{< button >}}
  a.book-btn {
    display: inline-block;
    font-size: $font-size-14;
    color: var(--color-link);
    line-height: $padding-16 * 2;
    padding: 0 $padding-16;
    border: $padding-1 solid var(--color-link);
    border-radius: $border-radius;
    cursor: pointer;

    &:hover {
      text-decoration: none;
    }
  }

  // {{< hint >}}
  .book-hint {
    @each $name, $color in $hint-colors {
      &.#{$name} {
        border-color: $color;
        background-color: rgba($color, 0.1);
      }
    }
  }
}
