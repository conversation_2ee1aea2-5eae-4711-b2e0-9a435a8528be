---
title: Credits
disableToc: true
---

## Contributors

Thanks to them <i class="fas fa-heart"></i> for making Open Source Software a better place !

{{% ghcontributors "https://api.github.com/repos/matcornic/hugo-theme-learn/contributors?per_page=100" %}}

And a special thanks to [@vjeantet](https://github.com/vjeantet) for his work on [docdock](https://github.com/vjeantet/hugo-theme-docdock), a fork of hugo-theme-learn. v2.0.0 of this theme is inspired by his work.

## Packages and libraries
* [mermaid](https://mermaid-js.github.io/) - generation of diagram and flowchart from text in a similar manner as markdown
* [font awesome](http://fontawesome.io/) - the iconic font and CSS framework
* [jQuery](https://jquery.com) - The Write Less, Do More, JavaScript Library
* [lunr](https://lunrjs.com) - Lunr enables you to provide a great search experience without the need for external, server-side, search services...
* [horsey](https://bevacqua.github.io/horsey/) - Progressive and customizable autocomplete component
* [clipboard.js](https://zenorocha.github.io/clipboard.js) - copy text to clipboard
* [highlight.js](https://highlightjs.org) - Javascript syntax highlighter
* [modernizr](https://modernizr.com) - A JavaScript toolkit that allows web developers to use new CSS3 and HTML5 features while maintaining a fine level of control over browsers that don't support

## Tooling

* [Netlify](https://www.netlify.com) - Continuous deployement and hosting of this documentation
* [Hugo](https://gohugo.io/)

