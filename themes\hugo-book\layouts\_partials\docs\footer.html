<div class="flex flex-wrap justify-between">

{{ if and .GitInfo .Site.Params.BookRepo }}
  <div>
    {{- $date := partial "docs/date" (dict "Date" .GitInfo.AuthorDate.Local "Format" .Site.Params.BookDateFormat) -}}
    <a class="flex align-center" href="{{ partial "docs/links/commit" . }}" title='{{ i18n "Last modified by" }} {{ .GitInfo.AuthorName }} | {{ $date }}' target="_blank" rel="noopener">
      <img src="{{ "svg/calendar.svg" | relURL }}" class="book-icon" alt="" />
      <span>{{ $date }}</span>
    </a>
  </div>
{{ end }}

{{ if and .File .Site.Params.BookRepo .Site.Params.BookEditPath }}
  <div>
    <a class="flex align-center" href="{{ partial "docs/links/edit" . }}" target="_blank" rel="noopener">
      <img src="{{ "svg/edit.svg" | relURL }}" class="book-icon" alt="" />
      <span>{{ i18n "Edit this page" }}</span>
    </a>
  </div>
{{ end }}

</div>

{{ $script := resources.Get "clipboard.js" | resources.Minify }}
{{ with $script.Content }}
  <script>{{ . | safeJS }}</script>
{{ end }}
