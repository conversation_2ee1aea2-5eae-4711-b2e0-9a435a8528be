{{- if .Page.Site.Params.BookPortableLinks -}}
  {{- template "portable-image" . -}}
{{- else -}}
  <img src="{{ .Destination | safeURL }}" alt="{{ .Text }}" {{ with .Title }}title="{{ . }}"{{ end }}/>
{{- end -}}

{{- define "portable-image" -}}
  {{- $isRemote := or (in .Destination "://") (strings.HasPrefix .Destination "//") }}
  {{- if not $isRemote }}
    {{- $path := print .Page.File.Dir .Destination }}
    {{- if strings.HasPrefix .Destination "/" }}
      {{- $path = print "/static" .Destination }}
    {{- end }}
    {{- if not (fileExists $path) }}
      {{- warnf "Image '%s' not found in '%s'" .Destination .Page.File }}
    {{- end }}
  {{- end }}
  <img src="{{ .Destination | safeURL }}" alt="{{ .Text }}" {{ with .Title }}title="{{ . }}"{{ end }}/>
{{- end -}}
