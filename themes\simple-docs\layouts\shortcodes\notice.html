{{ $type := .Get 0 | default "info" }}
<div class="notice notice-{{ $type }}">
    <div class="notice-icon">
        {{ if eq $type "warning" }}
            <i class="fas fa-exclamation-triangle"></i>
        {{ else if eq $type "note" }}
            <i class="fas fa-sticky-note"></i>
        {{ else if eq $type "tip" }}
            <i class="fas fa-lightbulb"></i>
        {{ else }}
            <i class="fas fa-info-circle"></i>
        {{ end }}
    </div>
    <div class="notice-content">
        {{ .Inner | markdownify }}
    </div>
</div>
