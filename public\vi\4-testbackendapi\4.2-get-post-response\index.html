<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>G<PERSON><PERSON> request GET/POST để kiểm tra response backend :: AWS System Manager</title>
    <link rel="stylesheet" href="/workshopFCJ/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <nav class="sidebar">
            <div class="logo">
    <a href="https://thuananwork.github.io/workshopFCJ/">
        <h2>AWS System Manager</h2>
    </a>
</div>

<nav class="menu">
    <ul>
        
    </ul>
    
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/1-introduce/">
            <strong>false. Giới thiệu</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/">
            <strong>false. Chuẩn bị môi trường</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/2-prerequiste/2.1-installnodejs/">
            false. Cài đặt NodeJS
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/2-prerequiste/2.2-installyarn/">
            false. Cài đặt Yarn
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/2-prerequiste/2.3-installsamcli/">
            false. Cài đặt SAM CLI
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/2-prerequiste/2.4-createiam/">
            false. Tạo tài khoản và cấu hình IAM
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/2-prerequiste/2.5-create-google-oauth2/">
            false. Tạo Google OAuth2 Project
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/2.1-installnodejs/">
            <strong>false. Cài đặt NodeJS</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/2.2-installyarn/">
            <strong>false. Cài đặt Yarn</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/2.3-installsamcli/">
            <strong>false. Cài đặt SAM CLI</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/2.4-createiam/">
            <strong>false. Tạo tài khoản và cấu hình IAM</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/2.5-create-google-oauth2/">
            <strong>false. Tạo Google OAuth2 Project</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/3-deploybackend/">
            <strong>false. Triển khai backend</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/3-deploybackend/3.1-deploy-backend/">
            false. Triển khai backend bằng SAM CLI/CloudFormation
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/3-deploybackend/3.2-check-status-log-backend/">
            false. Kiểm tra trạng thái và log backend sau khi triển khai
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/3-deploybackend/3.1-deploy-backend/">
            <strong>false. Triển khai backend bằng SAM CLI/CloudFormation</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/3-deploybackend/3.2-check-status-log-backend/">
            <strong>false. Kiểm tra trạng thái và log backend sau khi triển khai</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/4-testbackendapi/">
            <strong>false. Kiểm thử API backend với Postman</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/4-testbackendapi/4.1-endpoint-api-gateway/">
            false. Lấy endpoint API Gateway
        </a>
    </li>
    
    <li class="page-item active">
        <a href="/workshopFCJ/vi/4-testbackendapi/4.2-get-post-response/">
            false. Gửi request GET/POST để kiểm tra response backend
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/4-testbackendapi/4.1-endpoint-api-gateway/">
            <strong>false. Lấy endpoint API Gateway</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/4-testbackendapi/4.2-get-post-response/">
            <strong>false. Gửi request GET/POST để kiểm tra response backend</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/">
            <strong>false. Triển khai frontend</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.1-frontend-s3/">
            false. Triển khai frontend lên S3 Bucket
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.2-enable-static-hosting/">
            false. Bật static hosting, cấu hình CORS
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.3-s3-bucket-permission/">
            false. Cấp quyền public cho S3 Bucket
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.4-clientid-clientserver/">
            false. Cấu hình Google OAuth2 Client ID và Client Secret
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.5-connect-frontend-api-backend/">
            false. Kết nối frontend với API backend
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.1-frontend-s3/">
            <strong>false. Triển khai frontend lên S3 Bucket</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.2-enable-static-hosting/">
            <strong>false. Bật static hosting, cấu hình CORS</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.3-s3-bucket-permission/">
            <strong>false. Cấp quyền public cho S3 Bucket</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.4-clientid-clientserver/">
            <strong>false. Cấu hình Google OAuth2 Client ID và Client Secret</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.5-connect-frontend-api-backend/">
            <strong>false. Kết nối frontend với API backend</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/6-ssl-s3-static/">
            <strong>false. Thiết lập trang web SSL S3 Static</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/7-demo/">
            <strong>false. Demo và chạy dự án</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/8-cleanup/">
            <strong>false. Dọn dẹp tài nguyên</strong>
        </a>
    </li>
    
    
</ul>

    
</nav>



        </nav>
        <main class="content">
            
<article>
    <header>
        <h1>Gửi request GET/POST để kiểm tra response backend</h1>
    </header>
    <div class="article-content">
        <h4 id="gửi-request-getpost-để-kiểm-tra-response-backend-đăng-nhập">Gửi request GET/POST để kiểm tra response backend đăng nhập</h4>
<p>Sau khi đã lấy được endpoint API Gateway, bạn cần gửi các request <strong>POST</strong> và <strong>GET</strong> đến API để xác nhận backend hoạt động đúng. Việc này thường thực hiện bằng <strong>Postman</strong> – công cụ test API phổ biến nhất hiện nay.</p>
<p><strong>Các bước thực hiện:</strong></p>
<ol>
<li>
<p><strong>Kiểm thử phương thức POST bằng Postman</strong></p>
<ul>
<li>
<p>Mở Postman, chọn <strong>New</strong> → <strong>HTTP Request</strong>.</p>
</li>
<li>
<p>Chọn method là <strong>POST</strong>.</p>
</li>
<li>
<p>Nhập endpoint đầy đủ vào ô URL, ví dụ:</p>
<pre tabindex="0"><code>https://ars3538v1i.execute-api.ap-southeast-1.amazonaws.com/Prod/api/users
</code></pre></li>
<li>
<p>Chuyển sang tab <strong>Body</strong> → chọn <strong>raw</strong> → chọn <strong>JSON</strong>.
<img src="/images/test_api_postman.png" alt="Test-api-postman"></p>
</li>
<li>
<p><strong>Nhập dữ liệu mẫu vào ô body như sau:</strong></p>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-json" data-lang="json"><span style="display:flex;"><span>{
</span></span><span style="display:flex;"><span>  <span style="color:#f92672">&#34;username&#34;</span>: <span style="color:#e6db74">&#34;AdminUser&#34;</span>,
</span></span><span style="display:flex;"><span>  <span style="color:#f92672">&#34;email&#34;</span>: <span style="color:#e6db74">&#34;<EMAIL>&#34;</span>,
</span></span><span style="display:flex;"><span>  <span style="color:#f92672">&#34;password&#34;</span>: <span style="color:#e6db74">&#34;AdminUser123@&#34;</span>,
</span></span><span style="display:flex;"><span>  <span style="color:#f92672">&#34;firstName&#34;</span>: <span style="color:#e6db74">&#34;Admin&#34;</span>,
</span></span><span style="display:flex;"><span>  <span style="color:#f92672">&#34;lastName&#34;</span>: <span style="color:#e6db74">&#34;User&#34;</span>,
</span></span><span style="display:flex;"><span>  <span style="color:#f92672">&#34;isAdmin&#34;</span>: <span style="color:#66d9ef">true</span>,
</span></span><span style="display:flex;"><span>  <span style="color:#f92672">&#34;googleId&#34;</span>: <span style="color:#66d9ef">null</span>,
</span></span><span style="display:flex;"><span>  <span style="color:#f92672">&#34;avatar&#34;</span>: <span style="color:#e6db74">&#34;https://i.imgur.com/avatar.jpg&#34;</span>,
</span></span><span style="display:flex;"><span>  <span style="color:#f92672">&#34;phone&#34;</span>: <span style="color:#e6db74">&#34;0912345678&#34;</span>,
</span></span><span style="display:flex;"><span>  <span style="color:#f92672">&#34;address&#34;</span>: <span style="color:#e6db74">&#34;&#34;</span>,
</span></span><span style="display:flex;"><span>  <span style="color:#f92672">&#34;isDeleted&#34;</span>: <span style="color:#66d9ef">false</span>
</span></span><span style="display:flex;"><span>}
</span></span></code></pre></div></li>
<li>
<p>Nhấn <strong>Send</strong> để gửi request.</p>
</li>
<li>
<p>Nếu API hoạt động đúng, bạn sẽ nhận được response xác nhận.</p>
</li>
<li>
<p>Copy lại <code>token</code> để sử dụng cho các request sau.
<img src="/images/body_raw_postman_method_get.png" alt="Test-api-postman"></p>
</li>
</ul>
</li>
</ol>
<div class="notice notice-tip">
    <div class="notice-icon">
<pre><code>        &lt;i class=&quot;fas fa-lightbulb&quot;&gt;&lt;/i&gt;
    
&lt;/div&gt;
&lt;div class=&quot;notice-content&quot;&gt;
    Nếu API yêu cầu xác thực (&lt;code&gt;API Key&lt;/code&gt;, Bearer Token, v.v.) hoặc cần custom header, hãy bổ sung ở tab &lt;strong&gt;Headers&lt;/strong&gt; của Postman trước khi gửi request!
&lt;/div&gt;
</code></pre>
</div>
<ol start="2">
<li>
<p><strong>Kiểm thử phương thức GET đăng nhập bằng Postman</strong></p>
<ul>
<li>
<p>Truy cập vào <a href="https://ap-southeast-1.console.aws.amazon.com/dynamodbv2/home?region=ap-southeast-1">DynamoDB</a> trên AWS Controller</p>
</li>
<li>
<p>Chọn <strong>table</strong> chọn bảng <strong>ShopUser</strong>
<img src="/images/select_shopuser_db.png" alt="Test-api-postman"></p>
</li>
<li>
<p>Chọn <strong>Explore table items</strong> để kiểm tra dữ liệu của user vừa tạo
<img src="/images/explore_table_item.png" alt="Explore table item"></p>
</li>
<li>
<p>Cuộn xuống và chỉnh sửa quyền <strong>isAdmin</strong> của user vừa tạo thành <code>true</code>
<img src="/images/isAdmin_true.png" alt="IsAdmin"></p>
</li>
<li>
<p>Tạo một request mới trong Postman, chọn method <code>GET</code>.</p>
</li>
<li>
<p>Nhập Authorization đầy đủ vào ô URL, ví dụ:</p>
<pre tabindex="0"><code>https://ars3538v1i.execute-api.ap-southeast-1.amazonaws.com/Prod/api/users
</code></pre></li>
<li>
<p>Tại Authorization chọn: <strong>Bearer Token</strong></p>
</li>
<li>
<p>Tại ô Token: nhập token đã lưu trước đó</p>
</li>
<li>
<p>Nhấn <strong>Send</strong> để gửi request.
<img src="/images/test_api_postman_get_method.png" alt="Test-api-postman-get-method"></p>
</li>
<li>
<p>Nếu API hoạt động bình thường, bạn sẽ nhận được response trả về thông tin của các user đang tồn tại trong database.</p>
</li>
<li>
<p>Kiểm tra mã trạng thái HTTP (là 200 OK), nội dung body response.
<img src="/images/test_api_backend_success.png" alt="Test-api-backend-success"></p>
</li>
</ul>
</li>
</ol>
<div class="notice notice-tip">
    <div class="notice-icon">
<pre><code>        &lt;i class=&quot;fas fa-lightbulb&quot;&gt;&lt;/i&gt;
    
&lt;/div&gt;
&lt;div class=&quot;notice-content&quot;&gt;
    Bạn có thể test các chức năng khác của dự án bằng cách truy cập vào code để lấy url của api
&lt;/div&gt;
</code></pre>
</div>
<ol start="3">
<li>
<p><strong>Phân tích kết quả và xử lý lỗi thường gặp</strong></p>
<ul>
<li>Nếu nhận được response dữ liệu đúng → backend đã hoạt động chính xác.</li>
<li>Nếu gặp lỗi như <code>403 Forbidden</code>, <code>401 Unauthorized</code>, <code>Missing Authentication Token</code>:
<ul>
<li>Kiểm tra lại endpoint đã đúng route resource chưa.</li>
<li>Xem lại config API Gateway và mapping template.</li>
<li>Kiểm tra log Lambda trên CloudWatch để xác định lỗi chi tiết.</li>
</ul>
</li>
<li>Nếu mã lỗi <code>500 Internal Server Error</code>:
<ul>
<li>Thường là do code Lambda gặp lỗi. Vào CloudWatch log để debug.</li>
</ul>
</li>
</ul>
</li>
</ol>
<p><strong>Kết luận:</strong><br>
Việc kiểm thử API bằng POST/GET là bước xác nhận backend đã hoạt động hoàn chỉnh trước khi tích hợp với frontend hoặc triển khai lên môi trường thật. Nếu gặp lỗi, nên tra log trên CloudWatch hoặc kiểm tra lại cấu hình API Gateway và Lambda để xử lý kịp thời.</p>

        
    </div>
</article>

        </main>
    </div>
</body>
</html>
