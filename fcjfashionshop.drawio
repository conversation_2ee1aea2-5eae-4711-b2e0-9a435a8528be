<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/27.0.9 Chrome/134.0.6998.205 Electron/35.4.0 Safari/537.36" version="27.0.9">
  <diagram name="AWS Serverless Architecture" id="xBDizavzEtucA08D8CtQ">
    <mxGraphModel dx="2569" dy="2212" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="5iYREg2MAVDlLzS6mJbQ-16" value="&lt;font style=&quot;font-size: 24px;&quot;&gt;&lt;b&gt;AWS Cloud&lt;/b&gt;&lt;/font&gt;" style="points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;gradientColor=none;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_aws_cloud_alt;strokeColor=#232F3E;fillColor=none;verticalAlign=top;align=left;spacingLeft=30;fontColor=#232F3E;dashed=0;" parent="1" vertex="1">
          <mxGeometry x="-340" y="-1160" width="1670" height="1040" as="geometry" />
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-117" value="&lt;span style=&quot;font-size: 18px;&quot;&gt;Domain&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="5iYREg2MAVDlLzS6mJbQ-16" vertex="1">
          <mxGeometry x="180" y="160" width="90" height="40" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-27" value="" style="group" vertex="1" connectable="0" parent="5iYREg2MAVDlLzS6mJbQ-16">
          <mxGeometry x="450" y="770" width="90" height="108" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-25" value="" style="shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,iVBORw0KGgoAAAANSUhEUgAAAH0AAAB9CAMAAAC4XpwXAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAA2UExURd00TN9BV+hzhOZnef3y9P///+FNYvnZ3fvm6eqAj+yNmuNabu6ZpfTAx/CmsffM0vKzvAAAAAj11sEAAAASdFJOU///////////////////////AOK/vxIAAAAJcEhZcwAAFxEAABcRAcom8z8AAAKpSURBVGhD7ZqNrqQgDIUVpv4NqO//tBsQFCqOrY6zm2w/zUQheHJoq+K9VSUIgiAIgiAIgiAId6jVA+gayxwAz9BgnSIaD/sSGgsV0QCv9tuw1HHbbV4Ab9xWRAO0uO027d/2TlYX76hZDUrTarbALe/GlYzHKtxH4kbOm+yu0dJM5Fz23ryCqp3C0Zh207ga98bpvUyIuJ7d6ZT007jovcZm/VSYpIHExbg7ryjRJvIDa+OadzfvQ9a9GJlx2wnkuL9T78OuAKqqUgCA20645t0WM5w/9dfibgsTz3CyQh6hAbr1ZC6qd9RrrZBHELyTr7VCHpF5/5/VSXHXHSJGTndduClz1Jlx372Dx4rdjHDUmTP/mDpp5iuNSTqWAzziEL53AuQR/LgTII9YvffzPHcAkxlHtL8ArBkNdVl6RX2XS3vw0GPY6u45+pktPU7hqPu49wCdn+cSZrRbYdUmIYZjWE5D1nDUo/dPb49NVtYbUaTL5oet3p+qx5kvqofTq+on3p1kOKznhPjSY5bT8PbLVqd7J8BWR971MNklhQb/LcTFNek+ga2ee+99FH0xDDHEP/OuQwq7llV9t9Q9hq2eebcArXHrmtp9TdRK6eGH3ju/nmqTZ45b5cTjusm2+LOO9uPJ7/N7761XnwD62PI+qPeEZMVxz7ub9Nl9wFivmHh/Qj3P+XDBbfVI8J7MNVs9r3f/EQEmF8vf5/yaeNfrnZd1+D6/JF6q/rt7XaJeNQ7F9c5Tx95Ha9MLpPV+Dlsde0ckOU+ArY69I5J3GwLsrPvXvT8cdxhnvMXd2se9n4GHHsNWP7p7b3wKDIKddZVKFwklnlzHfRVRJ/CUOjnrAP8Z9ZiJtnO8PwJN3c3SE1DrU+mvb/T/PRAEQRAEQRAEQRCEMn8AYgsz4Msmx0kAAAAASUVORK5CYII=;fontSize=15;" vertex="1" parent="uIC1bcmi1Qysed_euvtZ-27">
          <mxGeometry x="6" width="78" height="78" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-26" value="&lt;span style=&quot;font-size: 15px; background-color: rgb(255, 255, 255);&quot;&gt;AWS IAM&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="uIC1bcmi1Qysed_euvtZ-27">
          <mxGeometry y="78" width="90" height="30" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-30" value="" style="group" vertex="1" connectable="0" parent="5iYREg2MAVDlLzS6mJbQ-16">
          <mxGeometry x="860" y="769" width="130" height="128" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-28" value="" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#E7157B;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloudformation;" vertex="1" parent="uIC1bcmi1Qysed_euvtZ-30">
          <mxGeometry x="31" width="78" height="78" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-29" value="&lt;span style=&quot;color: rgb(35, 47, 62); font-size: 15px;&quot;&gt;AWS&lt;/span&gt;&lt;div&gt;&lt;span style=&quot;color: rgb(35, 47, 62); font-size: 15px;&quot;&gt;CloudFormation&lt;/span&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="uIC1bcmi1Qysed_euvtZ-30">
          <mxGeometry y="78" width="130" height="50" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-43" value="" style="group" vertex="1" connectable="0" parent="5iYREg2MAVDlLzS6mJbQ-16">
          <mxGeometry x="1250" y="769" width="110" height="129" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-41" value="" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#232F3D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.command_line_interface;" vertex="1" parent="uIC1bcmi1Qysed_euvtZ-43">
          <mxGeometry x="15.5" width="79" height="79" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-42" value="&lt;span style=&quot;color: rgb(35, 47, 62); font-size: 15px;&quot;&gt;SAM CLI&lt;/span&gt;&lt;br style=&quot;color: rgb(35, 47, 62); font-size: 15px;&quot;&gt;&lt;span style=&quot;color: rgb(35, 47, 62); font-size: 15px;&quot;&gt;(Deployment)&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="uIC1bcmi1Qysed_euvtZ-43">
          <mxGeometry y="79" width="110" height="50" as="geometry" />
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-54" value="" style="endArrow=none;html=1;rounded=0;dashed=1;" parent="5iYREg2MAVDlLzS6mJbQ-16" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="340" y="920" as="sourcePoint" />
            <mxPoint x="340" y="720" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-55" value="" style="endArrow=none;html=1;rounded=0;dashed=1;" parent="5iYREg2MAVDlLzS6mJbQ-16" source="HMGAwLmAj25pJRaxAF2K-104" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="340" y="920" as="sourcePoint" />
            <mxPoint x="1620" y="920" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-56" value="" style="endArrow=none;html=1;rounded=0;dashed=1;" parent="5iYREg2MAVDlLzS6mJbQ-16" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="340" y="720" as="sourcePoint" />
            <mxPoint x="1620" y="720" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-57" value="" style="endArrow=none;html=1;rounded=0;dashed=1;" parent="5iYREg2MAVDlLzS6mJbQ-16" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1620" y="920" as="sourcePoint" />
            <mxPoint x="1620" y="720" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-80" value="&lt;font style=&quot;font-size: 18px;&quot;&gt;Sam Deploy&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="5iYREg2MAVDlLzS6mJbQ-16" vertex="1">
          <mxGeometry x="650" y="773" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="5iYREg2MAVDlLzS6mJbQ-17" value="" style="endArrow=none;html=1;rounded=0;dashed=1;" parent="5iYREg2MAVDlLzS6mJbQ-16" target="HMGAwLmAj25pJRaxAF2K-104" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="340" y="920" as="sourcePoint" />
            <mxPoint x="1500" y="920" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-104" value="&lt;font style=&quot;font-size: 24px;&quot;&gt;Deployment Environment&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="5iYREg2MAVDlLzS6mJbQ-16" vertex="1">
          <mxGeometry x="740" y="900" width="290" height="40" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-49" value="" style="group" vertex="1" connectable="0" parent="5iYREg2MAVDlLzS6mJbQ-16">
          <mxGeometry x="42.5" y="540" width="115" height="100" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-47" value="" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#232F3D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.users;" vertex="1" parent="uIC1bcmi1Qysed_euvtZ-49">
          <mxGeometry x="20" width="70" height="70" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-48" value="&lt;span style=&quot;color: rgb(35, 47, 62); font-size: 15px;&quot;&gt;&lt;b&gt;Users/Clients&lt;/b&gt;&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="uIC1bcmi1Qysed_euvtZ-49">
          <mxGeometry x="-5" y="70" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-47" value="" style="endArrow=none;html=1;rounded=0;dashed=1;" parent="5iYREg2MAVDlLzS6mJbQ-16" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="740" y="680" as="sourcePoint" />
            <mxPoint x="1140" y="680" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-17" value="" style="group" vertex="1" connectable="0" parent="5iYREg2MAVDlLzS6mJbQ-16">
          <mxGeometry x="578" y="390.00056338028173" width="78" height="121.69014084507043" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-15" value="" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#7AA116;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.s3;" vertex="1" parent="uIC1bcmi1Qysed_euvtZ-17">
          <mxGeometry width="78" height="78" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-16" value="&lt;span style=&quot;color: rgb(35, 47, 62); font-size: 15px;&quot;&gt;S3&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="uIC1bcmi1Qysed_euvtZ-17">
          <mxGeometry x="19" y="87.88732394366198" width="40" height="30" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-10" value="" style="group" vertex="1" connectable="0" parent="5iYREg2MAVDlLzS6mJbQ-16">
          <mxGeometry x="348" y="230.00169014084508" width="130" height="121.69014084507043" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-8" value="" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#DD344C;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.certificate_manager_3;" vertex="1" parent="uIC1bcmi1Qysed_euvtZ-10">
          <mxGeometry x="26" width="78" height="78" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-9" value="&lt;span style=&quot;color: rgb(35, 47, 62); font-size: 15px;&quot;&gt;AWS Certificate&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="uIC1bcmi1Qysed_euvtZ-10">
          <mxGeometry y="87.88732394366198" width="130" height="30" as="geometry" />
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-118" value="&lt;font style=&quot;font-size: 18px;&quot;&gt;SSL/TLS&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="5iYREg2MAVDlLzS6mJbQ-16" vertex="1">
          <mxGeometry x="430" y="339.99690140845064" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-65" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="5iYREg2MAVDlLzS6mJbQ-16" source="uIC1bcmi1Qysed_euvtZ-28" target="uIC1bcmi1Qysed_euvtZ-25">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-51" value="" style="endArrow=none;html=1;rounded=0;dashed=1;" parent="5iYREg2MAVDlLzS6mJbQ-16" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1620" y="680" as="sourcePoint" />
            <mxPoint x="1199" y="679.6300000000001" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-14" value="&lt;div&gt;&lt;ul&gt;&lt;li&gt;&lt;span style=&quot;font-size: 15px;&quot;&gt;ShopBrands&lt;/span&gt;&lt;/li&gt;&lt;li&gt;&lt;span style=&quot;font-size: 15px;&quot;&gt;ShopCarts&lt;/span&gt;&lt;/li&gt;&lt;li&gt;&lt;span style=&quot;font-size: 15px;&quot;&gt;ShopCategories&lt;/span&gt;&lt;/li&gt;&lt;li&gt;&lt;span style=&quot;font-size: 15px;&quot;&gt;ShopCoupons&lt;/span&gt;&lt;/li&gt;&lt;li&gt;&lt;span style=&quot;font-size: 15px;&quot;&gt;ShopOrderHistories&lt;/span&gt;&lt;/li&gt;&lt;li&gt;&lt;span style=&quot;font-size: 15px;&quot;&gt;ShopOrders&lt;/span&gt;&lt;/li&gt;&lt;li&gt;&lt;span style=&quot;font-size: 15px;&quot;&gt;ShopPayments&lt;/span&gt;&lt;/li&gt;&lt;li&gt;&lt;span style=&quot;font-size: 15px;&quot;&gt;ShopProducts&lt;/span&gt;&lt;/li&gt;&lt;li&gt;&lt;span style=&quot;font-size: 15px;&quot;&gt;ShopReviews&lt;/span&gt;&lt;/li&gt;&lt;li&gt;&lt;span style=&quot;font-size: 15px;&quot;&gt;ShopShippingCoupons&lt;/span&gt;&lt;/li&gt;&lt;li&gt;&lt;span style=&quot;font-size: 15px;&quot;&gt;ShopUsers&lt;/span&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=11;" parent="5iYREg2MAVDlLzS6mJbQ-16" vertex="1">
          <mxGeometry x="1420" y="336" width="200" height="240" as="geometry" />
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-52" value="" style="endArrow=none;html=1;rounded=0;dashed=1;" parent="5iYREg2MAVDlLzS6mJbQ-16" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1620" y="60" as="sourcePoint" />
            <mxPoint x="1620" y="680" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-75" value="&lt;font style=&quot;font-size: 18px;&quot;&gt;All Dynamodb&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 18px;&quot;&gt;Table(shopUser,ShopProduct,..)&lt;/font&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="5iYREg2MAVDlLzS6mJbQ-16" vertex="1">
          <mxGeometry x="1260" y="576" width="280" height="60" as="geometry" />
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-50" value="" style="endArrow=none;html=1;rounded=0;dashed=1;" parent="5iYREg2MAVDlLzS6mJbQ-16" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1199" y="679.6300000000001" as="sourcePoint" />
            <mxPoint x="1199" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-53" value="" style="endArrow=none;html=1;rounded=0;dashed=1;" parent="5iYREg2MAVDlLzS6mJbQ-16" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1199" y="60.00000000000003" as="sourcePoint" />
            <mxPoint x="1620" y="60.370000000000005" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-69" value="&lt;font style=&quot;font-size: 24px;&quot;&gt;Monitoring&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="HMGAwLmAj25pJRaxAF2K-53">
          <mxGeometry x="0.1729" y="2" relative="1" as="geometry">
            <mxPoint x="-46" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-36" value="" style="group" vertex="1" connectable="0" parent="5iYREg2MAVDlLzS6mJbQ-16">
          <mxGeometry x="1350" y="102.00000000000003" width="100" height="108" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-34" value="" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#E7157B;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloudwatch_2;" vertex="1" parent="uIC1bcmi1Qysed_euvtZ-36">
          <mxGeometry x="11" width="78" height="78" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-35" value="&lt;span style=&quot;color: rgb(35, 47, 62); font-size: 15px;&quot;&gt;CloudWatch&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="uIC1bcmi1Qysed_euvtZ-36">
          <mxGeometry y="78" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-40" value="" style="group" vertex="1" connectable="0" parent="5iYREg2MAVDlLzS6mJbQ-16">
          <mxGeometry x="1350" y="390" width="100" height="110" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-38" value="" style="shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,iVBORw0KGgoAAAANSUhEUgAAAH0AAAB9CAMAAAC4XpwXAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAA2UExURckl0cwz1NBA19NO2tpp3+GE5eeg6/LJ8/XW9vzx/P////jk+e678eut7uSS6Ndb3d134gAAAACbgQMAAAASdFJOU///////////////////////AOK/vxIAAAAJcEhZcwAAFxEAABcRAcom8z8AAAWZSURBVGhD7ZrZ0qMqEIBFkFXQ93/aU93g1oIhSuZM1fj9NXNBjG3vDabrXl5eXl5eXl7+ZRhjPMLoRz+EcRGk1sbuMGb0cvr1UzAR3EEsYZQT/UormCSSjTHOnZ5mnOkXGzCtYozyQUx7b7NeiKDc+gCtXTClW2spOP1sQ8watafrz2Ajilahp5+cEd4KuvYIDjY3Y4XoH4DC1f8ju2POWvOLOK5isNbUeZINPo+8nwTK2oGu5VFrylE8vbQaY21dARNU5sb9HLS2MoUU5OQgB/wH/8U/X32DHBDwdC0HqJ4z0mCto2v1gOUkXcygCgY21XGTAx33WfuS6rB+P+SjdGs/ZfxYUL20XgmELMq/zNqS6sw8iTmUzoaovxqK9bbk9VDjtQustX3XzckBZhxyKoLq2dZrrA107RuWqBmwdycjjH6YxTRNfTJGybvCWnPlsI9E3QE2ZGopflJUvfRU1RwzRoTxOMjhk5W8/jTmFt3Z3n5MzINXSmujDUgvqj5Yq+nad0TdvR1z0RYpqQ4xN/Y8/RXz5QrUHUxotc/foKg66Xp3Us9Y2zOcroCclKLqqUwsGPp5BSlsBXTKbMkuqt4xbSJR+p3Eh5vHJjWo7OMXVd8BtfqO8DgwLd/M6FhWfQX3A3enKyhyrtjjKlRHzTM+qyIGj87PCBjXxsCWEnaVmdrCIGL8M+HAOJ/vEbvvSmaIQuF0sRZIdd3HiIecH45OXppf4uyER5pDh3bw5XnbIWtocXx5CihjvId/uek13CwzCbdOVXzfZKkk1nUgnSoJwjVd/IKtw4KMOWxdluoZzoYHvzwRDtJJtjExB6+Uo1nuTrMzBOwj4TjT0rUs/GAl4LHmUbrON7cjgTbz55ov83yx1G1oUsobaL7uJvQn+TTiQbirsdkloDjKd/LyXmSKmk2qE8+AWGIyGkBJGucb6mB4qE2npLhBNGi/1nMtA88YAQy/LUP8kwS4x3qbwzTvnBqlDzJIOaKKB8NzqMoNND/UOjYnDxzBEjPudvlgB1cegb+BmlAESbY04G62UxaEn8rwTaLfGUm4Xog5RHBjuzsfaaj5ovto3XCVP5vh0eethEfpE5p4LD4AHGhGW/eQavCagg9kDrmFtnZm/RJuBaXEYngQHsuyPnW8O8h04wFnm0IOL4YHG8Txt2LQrgGCKGYyD7q0I01BDsKj5uAqOmrcAqt8SqCC36dY0xlMXiAcK3OLQhttb22saAVGnBxReOg6FtBJhRD5kvRexKpijwWDT/E6qDxp41y8/CvgpmmY9fk3M+BkhluWGHpg9jZBh7cOHV+rqw8zva2E/QJckAqOHBh6gVx2h+XYiceUSzjvJZRZtAWM/KvmiX3hfwD6NMJ3w3wC/AzNPAXcjpEu3ILoMA2HFgcCYL9yPsf1dQfrH8gez/cTdDgvPZgleoRs0MHybXQ/p+5eErZzuk3dMuAZeO+rfoGGJ2ZH4c2kX+1mwPDqIJyletNGOjoWXZwBu9BeOAtYm1wrv/PY3XV2O8H0XjhL19pBttK9X9+/W5kZWJYfAjAetbbWBQYFsJX0rpu2YVrjbw9WOzDWc6gCayl0WAGbSs+/HHAxJvYrvo+mkK38vrl7OtfaHdqHzS+NdV9gU5BKH1qO014OSeeFltKnTLz37OrQv5l0hvvjUsIXaON3Bz1uKV5fPEAb3eNxzDJa1Itvs5OEkyeI5JjwJS+fgKurL75g2xVOvvrEFXpck6EWT0EyIX8BFubSvudL4ga2Xv6E1b7aTJ/oY/PwNRnE40amjdkTS8hfHZl1Hce5Gq+rNlQd8cdrgFNhPvy8Dl4QiFluh/eFd5aPWLv3gnNOa0eanFMNsryAoE9wwKldj/sRkwhq9zNGFOuUDOuLmj8B6zmf+PSHf0/78vLy8vLy8vL38x9h0VIaXmZ8yQAAAABJRU5ErkJggg==;fontSize=15;" vertex="1" parent="uIC1bcmi1Qysed_euvtZ-40">
          <mxGeometry x="10" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-39" value="&lt;span style=&quot;font-size: 15px; background-color: rgb(255, 255, 255);&quot;&gt;DynamoDB&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="uIC1bcmi1Qysed_euvtZ-40">
          <mxGeometry y="80" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-64" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="5iYREg2MAVDlLzS6mJbQ-16" source="uIC1bcmi1Qysed_euvtZ-35" target="uIC1bcmi1Qysed_euvtZ-38">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-37" value="" style="endArrow=none;html=1;rounded=0;dashed=1;" parent="5iYREg2MAVDlLzS6mJbQ-16" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="340" y="680" as="sourcePoint" />
            <mxPoint x="340" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-72" value="&lt;font style=&quot;font-size: 15px;&quot;&gt;Request&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="HMGAwLmAj25pJRaxAF2K-37">
          <mxGeometry x="-0.8909" relative="1" as="geometry">
            <mxPoint y="-5" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-40" value="" style="endArrow=none;html=1;rounded=0;dashed=1;" parent="5iYREg2MAVDlLzS6mJbQ-16" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="340" y="678.9459154929579" as="sourcePoint" />
            <mxPoint x="700" y="680" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-33" value="" style="group" vertex="1" connectable="0" parent="5iYREg2MAVDlLzS6mJbQ-16">
          <mxGeometry x="895" y="390" width="80" height="108" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-31" value="" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#ED7100;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.lambda;" vertex="1" parent="uIC1bcmi1Qysed_euvtZ-33">
          <mxGeometry width="78" height="78" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-32" value="&lt;span style=&quot;color: rgb(35, 47, 62); font-size: 15px;&quot;&gt;Lambda&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="uIC1bcmi1Qysed_euvtZ-33">
          <mxGeometry y="78" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-52" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="5iYREg2MAVDlLzS6mJbQ-16" source="uIC1bcmi1Qysed_euvtZ-32" target="uIC1bcmi1Qysed_euvtZ-28">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="935" y="700" />
              <mxPoint x="935" y="700" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-77" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="5iYREg2MAVDlLzS6mJbQ-16" source="uIC1bcmi1Qysed_euvtZ-31" target="uIC1bcmi1Qysed_euvtZ-15">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-85" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.555;entryY=0.99;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="5iYREg2MAVDlLzS6mJbQ-16" source="uIC1bcmi1Qysed_euvtZ-48" target="uIC1bcmi1Qysed_euvtZ-42">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="95" y="980" />
              <mxPoint x="1311" y="980" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-38" value="" style="endArrow=none;html=1;rounded=0;dashed=1;" parent="5iYREg2MAVDlLzS6mJbQ-16" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="340" y="60" as="sourcePoint" />
            <mxPoint x="700" y="60" as="targetPoint" />
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-71" value="&lt;font style=&quot;font-size: 24px;&quot;&gt;Fronend&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="HMGAwLmAj25pJRaxAF2K-38">
          <mxGeometry x="-0.2716" y="-2" relative="1" as="geometry">
            <mxPoint x="49" y="-2" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-49" value="" style="endArrow=none;html=1;rounded=0;dashed=1;" parent="5iYREg2MAVDlLzS6mJbQ-16" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="740" y="60" as="sourcePoint" />
            <mxPoint x="1140" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-70" value="&lt;font style=&quot;font-size: 24px;&quot;&gt;Backend API&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="HMGAwLmAj25pJRaxAF2K-49">
          <mxGeometry x="-0.011" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-61" value="&lt;div style=&quot;text-align: left;&quot;&gt;&lt;span style=&quot;font-size: 18px; background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;GET/Users/<USER>/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left;&quot;&gt;&lt;span style=&quot;font-size: 18px; background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;POST/Users/<USER>/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left;&quot;&gt;&lt;span style=&quot;font-size: 18px;&quot;&gt;PUT/Users/<USER>/span&gt;&lt;span style=&quot;font-size: 18px; background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left;&quot;&gt;&lt;span style=&quot;font-size: 18px;&quot;&gt;DELETE/Users/<USER>/span&gt;&lt;span style=&quot;font-size: 18px;&quot;&gt;&lt;/span&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="5iYREg2MAVDlLzS6mJbQ-16" vertex="1">
          <mxGeometry x="949.9978378378378" y="220" width="180" height="100" as="geometry" />
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-48" value="" style="endArrow=none;html=1;rounded=0;dashed=1;" parent="5iYREg2MAVDlLzS6mJbQ-16" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1141" y="680" as="sourcePoint" />
            <mxPoint x="1140" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-90" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="5iYREg2MAVDlLzS6mJbQ-16" source="uIC1bcmi1Qysed_euvtZ-31" target="uIC1bcmi1Qysed_euvtZ-38">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1070" y="430" />
              <mxPoint x="1070" y="430" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-91" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.25;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="5iYREg2MAVDlLzS6mJbQ-16" source="uIC1bcmi1Qysed_euvtZ-31" target="uIC1bcmi1Qysed_euvtZ-34">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1170" y="410" />
              <mxPoint x="1170" y="141" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-95" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.504;entryY=1.044;entryDx=0;entryDy=0;entryPerimeter=0;endArrow=none;startFill=0;" edge="1" parent="5iYREg2MAVDlLzS6mJbQ-16" source="HMGAwLmAj25pJRaxAF2K-75" target="uIC1bcmi1Qysed_euvtZ-39">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-98" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="5iYREg2MAVDlLzS6mJbQ-16" source="uIC1bcmi1Qysed_euvtZ-28" target="uIC1bcmi1Qysed_euvtZ-41">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-99" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.75;exitDx=0;exitDy=0;entryX=0.75;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="5iYREg2MAVDlLzS6mJbQ-16" source="uIC1bcmi1Qysed_euvtZ-38" target="uIC1bcmi1Qysed_euvtZ-28">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1170" y="450" />
              <mxPoint x="1170" y="700" />
              <mxPoint x="950" y="700" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-102" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="33.42857142857156" y="-790.5714285714284" as="targetPoint" />
            <mxPoint x="34" y="-791" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-92" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" edge="1" target="uIC1bcmi1Qysed_euvtZ-1" source="uIC1bcmi1Qysed_euvtZ-47">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-140" y="-630" as="sourcePoint" />
            <mxPoint x="240" y="-640" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-115" value="" style="endArrow=none;html=1;rounded=0;dashed=1;entryX=0.542;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="-480" as="sourcePoint" />
            <mxPoint x="400.9799999999998" y="-670" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-45" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="HMGAwLmAj25pJRaxAF2K-31" target="uIC1bcmi1Qysed_euvtZ-19">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-50" y="-962" />
              <mxPoint x="-50" y="-1011" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-46" value="" style="endArrow=none;html=1;rounded=0;dashed=1;exitX=0.539;exitY=-0.042;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400.4099999999996" y="-671.6800000000001" as="sourcePoint" />
            <mxPoint x="400" y="-1100" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-24" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="540" y="-1050" width="110" height="108" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-22" value="" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#E7157B;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.api_gateway;" vertex="1" parent="uIC1bcmi1Qysed_euvtZ-24">
          <mxGeometry x="16" width="78" height="78" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-23" value="&lt;span style=&quot;color: rgb(35, 47, 62); font-size: 15px;&quot;&gt;API Gateway&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="uIC1bcmi1Qysed_euvtZ-24">
          <mxGeometry y="78" width="110" height="30" as="geometry" />
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-39" value="" style="endArrow=none;html=1;rounded=0;dashed=1;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="360" y="-480" as="sourcePoint" />
            <mxPoint x="360" y="-1100" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-74" value="&lt;font style=&quot;font-size: 15px;&quot;&gt;Manager permission&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="HMGAwLmAj25pJRaxAF2K-39">
          <mxGeometry x="-0.5009" y="-3" relative="1" as="geometry">
            <mxPoint x="17" y="-8" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-3" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="222" y="-619.9953521126761" width="110" height="144.22535211267606" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-1" value="" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#E7157B;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.api_gateway;" vertex="1" parent="uIC1bcmi1Qysed_euvtZ-3">
          <mxGeometry x="16" width="78" height="78" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-2" value="&lt;span style=&quot;color: rgb(35, 47, 62); font-size: 15px;&quot;&gt;AWS&lt;/span&gt;&lt;div&gt;&lt;span style=&quot;color: rgb(35, 47, 62); font-size: 15px;&quot;&gt;API Gateway&lt;/span&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="uIC1bcmi1Qysed_euvtZ-3">
          <mxGeometry y="87.88732394366198" width="110" height="50" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-14" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="23" y="-769.9994366197183" width="100" height="121.69014084507043" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-12" value="" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#8C4FFF;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.cloudfront;" vertex="1" parent="uIC1bcmi1Qysed_euvtZ-14">
          <mxGeometry x="8" width="78" height="78" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-13" value="&lt;span style=&quot;color: rgb(35, 47, 62); font-size: 15px;&quot;&gt;CloudFront&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="uIC1bcmi1Qysed_euvtZ-14">
          <mxGeometry y="87.88732394366198" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-97" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" edge="1" target="uIC1bcmi1Qysed_euvtZ-15" source="uIC1bcmi1Qysed_euvtZ-12">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="118" y="-750" as="sourcePoint" />
            <mxPoint x="258" y="-750" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-50" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="uIC1bcmi1Qysed_euvtZ-16" target="uIC1bcmi1Qysed_euvtZ-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-51" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="uIC1bcmi1Qysed_euvtZ-1" target="uIC1bcmi1Qysed_euvtZ-28">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-56" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="uIC1bcmi1Qysed_euvtZ-9">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="73" y="-770" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-86" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="-300" y="-1002" width="120" height="112" as="geometry" />
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-31" value="" style="ellipse;shape=cloud;whiteSpace=wrap;html=1;" parent="uIC1bcmi1Qysed_euvtZ-86" vertex="1">
          <mxGeometry width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="HMGAwLmAj25pJRaxAF2K-32" value="&lt;font style=&quot;font-size: 24px;&quot;&gt;internet&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="uIC1bcmi1Qysed_euvtZ-86" vertex="1">
          <mxGeometry x="30" y="80" width="60" height="32" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-87" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="HMGAwLmAj25pJRaxAF2K-32" target="uIC1bcmi1Qysed_euvtZ-47">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-242" y="-820" />
              <mxPoint x="-242" y="-820" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-89" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="uIC1bcmi1Qysed_euvtZ-20" target="uIC1bcmi1Qysed_euvtZ-8">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="180" y="-891" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-92" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="uIC1bcmi1Qysed_euvtZ-19" target="uIC1bcmi1Qysed_euvtZ-22">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-94" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="uIC1bcmi1Qysed_euvtZ-23" target="uIC1bcmi1Qysed_euvtZ-31">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="595" y="-830" />
              <mxPoint x="595" y="-830" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-102" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="142" y="-1049.9967605633801" width="80" height="107.99732394366197" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-19" value="" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#8C4FFF;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.route_53;" vertex="1" parent="uIC1bcmi1Qysed_euvtZ-102">
          <mxGeometry x="1" width="78" height="78" as="geometry" />
        </mxCell>
        <mxCell id="uIC1bcmi1Qysed_euvtZ-20" value="&lt;span style=&quot;color: rgb(35, 47, 62); font-size: 15px;&quot;&gt;Route 53&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="uIC1bcmi1Qysed_euvtZ-102">
          <mxGeometry y="77.99732394366197" width="80" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
