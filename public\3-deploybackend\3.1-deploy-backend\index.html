<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deploying the Backend with SAM CLI/CloudFormation :: AWS System Manager</title>
    <link rel="stylesheet" href="/workshopFCJ/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <nav class="sidebar">
            <div class="logo">
    <a href="https://thuananwork.github.io/workshopFCJ/">
        <h2>AWS System Manager</h2>
    </a>
</div>

<nav class="menu">
    <ul>
        
    </ul>
    
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/1-introduce/">
            <strong>false. Introduction</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/">
            <strong>false. Preparation</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.1-installnodejs/">
            false. Installing NodeJS
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.2-installyarn/">
            false. Installing Yarn
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.3-installsamcli/">
            false. Installing SAM CLI
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.4-createiam/">
            false. Create Account and Configure IAM
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.5-create-google-oauth2/">
            false. Create Google OAuth2 Project
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.1-installnodejs/">
            <strong>false. Installing NodeJS</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.2-installyarn/">
            <strong>false. Installing Yarn</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.3-installsamcli/">
            <strong>false. Installing SAM CLI</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.4-createiam/">
            <strong>false. Create Account and Configure IAM</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.5-create-google-oauth2/">
            <strong>false. Create Google OAuth2 Project</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/3-deploybackend/">
            <strong>false. Deploying the Backend</strong>
        </a>
    </li>
    
    <li class="page-item active">
        <a href="/workshopFCJ/3-deploybackend/3.1-deploy-backend/">
            false. Deploying the Backend with SAM CLI/CloudFormation
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/3-deploybackend/3.2-check-status-log-backend/">
            false. Checking Backend Status and Logs After Deployment
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/3-deploybackend/3.1-deploy-backend/">
            <strong>false. Deploying the Backend with SAM CLI/CloudFormation</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/3-deploybackend/3.2-check-status-log-backend/">
            <strong>false. Checking Backend Status and Logs After Deployment</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/4-testbackendapi/">
            <strong>false. Testing Backend APIs with Postman</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/4-testbackendapi/4.1-endpoint-api-gateway/">
            false. Retrieving the API Gateway Endpoint
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/4-testbackendapi/4.2-get-post-response/">
            false. Sending GET/POST Requests to Verify Backend Responses
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/4-testbackendapi/4.1-endpoint-api-gateway/">
            <strong>false. Retrieving the API Gateway Endpoint</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/4-testbackendapi/4.2-get-post-response/">
            <strong>false. Sending GET/POST Requests to Verify Backend Responses</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/">
            <strong>false. Deploy Frontend</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.1-frontend-s3/">
            false. Deploy Frontend to S3 Bucket
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.2-enable-static-hosting/">
            false. Enable Static Hosting, Configure CORS, and Set S3 Bucket Permissions
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.3-s3-bucket-permission/">
            false. Grant Public Permissions to S3 Bucket
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.4-clientid-clientserver/">
            false. Configure Google OAuth2 Client ID and Client Secret
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.5-connect-frontend-api-backend/">
            false. Connect the Frontend to the Backend API
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.1-frontend-s3/">
            <strong>false. Deploy Frontend to S3 Bucket</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.2-enable-static-hosting/">
            <strong>false. Enable Static Hosting, Configure CORS, and Set S3 Bucket Permissions</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.3-s3-bucket-permission/">
            <strong>false. Grant Public Permissions to S3 Bucket</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.4-clientid-clientserver/">
            <strong>false. Configure Google OAuth2 Client ID and Client Secret</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.5-connect-frontend-api-backend/">
            <strong>false. Connect the Frontend to the Backend API</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/6-ssl-s3-static/">
            <strong>false. Setup SSL S3 Static Website</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/7-demo/">
            <strong>false. Demo and Run the Project</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/8-cleanup/">
            <strong>false. Clean Up Resources</strong>
        </a>
    </li>
    
    
</ul>

    
</nav>



        </nav>
        <main class="content">
            
<article>
    <header>
        <h1>Deploying the Backend with SAM CLI/CloudFormation</h1>
    </header>
    <div class="article-content">
        <h4 id="deploying-the-backend-with-sam-clicloudformation">Deploying the Backend with SAM CLI/CloudFormation</h4>
<p>In this section, you will deploy the backend for your dynamic e-commerce website using <strong>SAM CLI</strong> and <strong>CloudFormation</strong>. We will use the provided <strong><code>template.yaml</code></strong> file in your project to automatically create resources such as <strong>API Gateway</strong>, <strong>Lambda</strong>, and <strong>DynamoDB</strong>.</p>
<h4 id="step-1-configure-aws-cli">Step 1: Configure AWS CLI</h4>
<p>Before using <strong>SAM CLI</strong> for deployment, you need to configure <strong>AWS CLI</strong> with your AWS account credentials. This allows <strong>SAM CLI</strong> to use the access rights granted to your IAM User.</p>
<ol>
<li>
<p><strong>Run the <code>aws configure</code> command</strong>:</p>
<ul>
<li>
<p>Open your terminal and enter the following command to configure AWS CLI:</p>
<pre><code> aws configure
</code></pre>
</li>
</ul>
</li>
<li>
<p><strong>Enter your configuration information</strong>:</p>
<ul>
<li>
<p><strong>AWS Access Key ID</strong>: Enter the <strong>Access Key ID</strong> generated when creating your IAM User.</p>
</li>
<li>
<p><strong>AWS Secret Access Key</strong>: Enter the <strong>Secret Access Key</strong> associated with your Access Key ID.</p>
</li>
<li>
<p><strong>Default region name</strong>: Enter <code>ap-southeast-1</code> (Singapore).</p>
</li>
<li>
<p><strong>Default output format</strong>: You can enter <code>json</code> or leave it as the default (<code>None</code>).
<img src="/images/aws_configure.png" alt="Error image"></p>
</li>
<li>
<p>Once finished, AWS CLI will save the configuration in the <code>~/.aws/credentials</code> file (Linux/macOS) or <code>C:\Users\<USER>\.aws\credentials</code> (Windows).</p>
</li>
</ul>
</li>
</ol>
<h4 id="step-2-deploy-the-backend-with-sam-cli">Step 2: Deploy the Backend with SAM CLI</h4>
<ol>
<li>
<p><strong>Check the <code>template.yaml</code> file</strong>:</p>
<ul>
<li>If you don’t have a <strong><code>template.yaml</code></strong> file, check your project directory. This file defines resources like <strong>API Gateway</strong>, <strong>Lambda</strong>, and <strong>DynamoDB</strong>.</li>
<li>If the file exists, make sure it is correctly configured for the resources you need.</li>
</ul>
</li>
<li>
<p><strong>Build the project</strong>:</p>
<ul>
<li>
<p>After ensuring that <strong><code>template.yaml</code></strong> is in your project,</p>
</li>
<li>
<p>Navigate to your backend project folder and use <strong>SAM CLI</strong> to build:</p>
<pre><code> sam build
</code></pre>
</li>
<li>
<p>If you see <strong>Build Succeeded</strong>, you have successfully installed and configured SAM CLI.
<img src="/images/build_success_samcli.png" alt="Error image"></p>
</li>
</ul>
</li>
<li>
<p><strong>Validate the Template</strong>:</p>
<ul>
<li>
<p>Check your <code>template.yaml</code> configuration to ensure there are no syntax or configuration errors:</p>
</li>
<li>
<p>Run <strong>sam validate</strong> to check your template:</p>
<pre><code> sam validate
</code></pre>
</li>
<li>
<p>If you see the message below, your <strong>template.yaml</strong> is valid.
<img src="/images/sam_validate.png" alt="Error image"></p>
</li>
</ul>
</li>
<li>
<p><strong>Deploy resources to AWS</strong>:</p>
<ul>
<li>
<p>After building and validating, deploy resources to AWS using:</p>
<pre><code> sam deploy --guided
</code></pre>
</li>
<li>
<p>When using <strong>SAM CLI</strong> to deploy, you’ll be prompted for configuration options. Suggested values:</p>
<ul>
<li><strong>Stack Name</strong>: <code>sam-app</code></li>
<li><strong>AWS Region</strong>: <code>ap-southeast-1</code></li>
<li><strong>Confirm changes before deploy</strong>: <code>y</code></li>
<li><strong>Allow SAM CLI IAM role creation</strong>: <code>y</code></li>
<li><strong>Disable rollback</strong>: <code>n</code> (do not disable rollback)</li>
<li><strong>ExpressApiFunction has no authentication. Is this okay?</strong>: <code>y</code></li>
<li><strong>Save arguments to configuration file</strong>: <code>y</code></li>
<li><strong>SAM configuration file</strong>: Press <strong>Enter</strong></li>
<li><strong>SAM configuration environment</strong>: Press <strong>Enter</strong></li>
</ul>
<p><img src="/images/deploy_this_changeset.png" alt="Error image"></p>
<ul>
<li><strong>Deploy this changeset</strong>: <code>y</code></li>
<li>Wait about 5 minutes for serverless resources (API Gateway, Lambda, DynamoDB, etc.) to be set up from your machine to AWS.</li>
</ul>
<p><img src="/images/create_sam_app_success.png" alt="Error image"></p>
</li>
</ul>
</li>
<li>
<p><strong>Verify Deployment</strong>:</p>
<ul>
<li>After successful deployment, SAM CLI will provide information about the resources created. You can check <strong>API Gateway</strong>, <strong>Lambda function</strong>, <strong>S3</strong>, <strong>CloudFormation</strong>, and <strong>DynamoDB</strong> in the AWS Management Console to confirm that everything has been set up correctly.</li>
</ul>
</li>
</ol>
<div class="notice notice-tip">
    <div class="notice-icon">
<pre><code>        &lt;i class=&quot;fas fa-lightbulb&quot;&gt;&lt;/i&gt;
    
&lt;/div&gt;
&lt;div class=&quot;notice-content&quot;&gt;
    Your backend has been successfully deployed! Resources like API Gateway, Lambda function, and DynamoDB have been automatically created. You can check them in the AWS Console.
&lt;/div&gt;
</code></pre>
</div>
<pre><code> - Go to [API Gateway](https://ap-southeast-1.console.aws.amazon.com/apigateway/main/) to see your API Gateway.

  ![Error image](/images/create_API_gateway.png)

 - Go to [Lambda function](https://ap-southeast-1.console.aws.amazon.com/lambda/home/<USER>

  ![Error image](/images/create_auto_lambda.png)

 - Go to [Amazon S3](https://ap-southeast-1.console.aws.amazon.com/s3/home) to see your S3 buckets.

  ![Error image](/images/access_s3_after_setup_samcli.png)

 - Go to [CloudFormation](https://ap-southeast-1.console.aws.amazon.com/cloudformation/home) to see your CloudFormation stacks.

  ![Error image](/images/create_auto_cloud_foumation.png)

 - Go to [DynamoDB](https://ap-southeast-1.console.aws.amazon.com/dynamodbv2/home?region=ap-southeast-1#tables) to see your DynamoDB tables.

  ![Error image](/images/create_auto_dynamoDB.png)
</code></pre>

        
    </div>
</article>

        </main>
    </div>
</body>
</html>
