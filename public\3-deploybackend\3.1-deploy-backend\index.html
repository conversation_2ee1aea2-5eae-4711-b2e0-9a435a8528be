<!DOCTYPE html>
<html lang="en" class="js csstransforms3d">
  <head><script src="/livereload.js?mindelay=10&amp;v=2&amp;port=1313&amp;path=livereload" data-no-instant defer></script>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="generator" content="Hugo 0.147.8">
    <meta name="description" content="">
<meta name="author" content="<EMAIL>">

    <link rel="icon" href="/images/favicon.png" type="image/png">

    <title>Deploying the Backend with SAM CLI/CloudFormation :: AWS System Manager</title>

    
    <link href="/css/nucleus.css?1751832716" rel="stylesheet">
    <link href="/css/fontawesome-all.min.css?1751832716" rel="stylesheet">
    <link href="/css/hybrid.css?1751832716" rel="stylesheet">
    <link href="/css/featherlight.min.css?1751832716" rel="stylesheet">
    <link href="/css/perfect-scrollbar.min.css?1751832716" rel="stylesheet">
    <link href="/css/auto-complete.css?1751832716" rel="stylesheet">
    <link href="/css/atom-one-dark-reasonable.css?1751832716" rel="stylesheet">
    <link href="/css/theme.css?1751832716" rel="stylesheet">
    <link href="/css/hugo-theme.css?1751832716" rel="stylesheet">
    
    <link href="/css/theme-workshop.css?1751832716" rel="stylesheet">
    
    

    <script src="/js/jquery-3.3.1.min.js?1751832716"></script>

    <style>
      :root #header + #content > #left > #rlblock_left{
          display:none !important;
      }
      
    </style>
    
  </head>
  <body class="" data-url="/3-deploybackend/3.1-deploy-backend/">
    <nav id="sidebar" class="showVisitedLinks">

  
  
  <div id="header-wrapper">
    <div id="header">
      <a id="logo" href="/">

<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 30" width="30%"><defs><style>.cls-1{fill:#fff;}.cls-2{fill:#f90;fill-rule:evenodd;}</style></defs><title>AWS-Logo_White-Color</title><path class="cls-1" d="M14.09,10.85a4.7,4.7,0,0,0,.19,1.48,7.73,7.73,0,0,0,.54,**********,0,0,1,.***********,0,0,1-.32.49l-1,.7a.83.83,0,0,1-.***********,0,0,1-.49-.23,3.8,3.8,0,0,1-.6-.77q-.25-.42-.51-1a6.14,6.14,0,0,1-4.89,2.3,4.54,4.54,0,0,1-3.32-1.19,4.27,4.27,0,0,1-1.22-3.2A4.28,4.28,0,0,1,3.61,7.75,6.06,6.06,0,0,1,7.69,6.46a12.47,12.47,0,0,1,1.76.13q.92.13,1.91.36V5.73a3.65,3.65,0,0,0-.79-2.66A3.81,3.81,0,0,0,7.86,2.3a7.71,7.71,0,0,0-1.79.22,12.78,12.78,0,0,0-1.79.57,4.55,4.55,0,0,1-.58.22l-.26,0q-.35,0-.35-.52V2a1.09,1.09,0,0,1,.12-.58,1.2,1.2,0,0,1,.47-.35A10.88,10.88,0,0,1,5.77.32,10.19,10.19,0,0,1,8.36,0a6,6,0,0,1,4.35,1.35,5.49,5.49,0,0,1,1.38,4.09ZM7.34,13.38a5.36,5.36,0,0,0,1.72-.31A3.63,3.63,0,0,0,10.63,12,2.62,2.62,0,0,0,11.19,11a5.63,5.63,0,0,0,.16-1.44v-.7a14.35,14.35,0,0,0-1.53-.28,12.37,12.37,0,0,0-1.56-.1,3.84,3.84,0,0,0-2.47.67A2.34,2.34,0,0,0,5,11a2.35,2.35,0,0,0,.61,1.76A2.4,2.4,0,0,0,7.34,13.38Zm13.35,1.8a1,1,0,0,1-.64-.16,1.3,1.3,0,0,1-.35-.65L15.81,1.51a3,3,0,0,1-.15-.67.36.36,0,0,1,.41-.41H17.7a1,1,0,0,1,.65.16,1.4,1.4,0,0,1,.33.65l2.79,11,2.59-11A1.17,1.17,0,0,1,24.39.6a1.1,1.1,0,0,1,.67-.16H26.4a1.1,1.1,0,0,1,.67.16,1.17,1.17,0,0,1,.32.65L30,12.39,32.88,1.25A1.39,1.39,0,0,1,33.22.6a1,1,0,0,1,.65-.16h1.54a.36.36,0,0,1,.41.41,1.36,1.36,0,0,1,0,.26,3.64,3.64,0,0,1-.12.41l-4,12.86a1.3,1.3,0,0,1-.35.65,1,1,0,0,1-.64.16H29.25a1,1,0,0,1-.67-.17,1.26,1.26,0,0,1-.32-.67L25.67,3.64,23.11,14.34a1.26,1.26,0,0,1-.32.67,1,1,0,0,1-.67.17Zm21.36.44a11.28,11.28,0,0,1-2.56-.29,7.44,7.44,0,0,1-1.92-.67,1,1,0,0,1-.61-.93v-.84q0-.52.38-.52a.9.9,0,0,1,.31.06l.42.17a8.77,8.77,0,0,0,1.83.58,9.78,9.78,0,0,0,2,.2,4.48,4.48,0,0,0,2.43-.55,1.76,1.76,0,0,0,.86-1.57,1.61,1.61,0,0,0-.45-1.16A4.29,4.29,0,0,0,43,9.22l-2.41-.76A5.15,5.15,0,0,1,38,6.78a3.94,3.94,0,0,1-.83-2.41,3.7,3.7,0,0,1,.45-1.85,4.47,4.47,0,0,1,1.19-1.37A5.27,5.27,0,0,1,40.51.29,7.4,7.4,0,0,1,42.6,0a8.87,8.87,0,0,1,1.12.07q.57.07,1.08.19t.95.26a4.27,4.27,0,0,1,.7.29,1.59,1.59,0,0,1,.49.41.94.94,0,0,1,.15.55v.79q0,.52-.38.52a1.76,1.76,0,0,1-.64-.2,7.74,7.74,0,0,0-3.2-.64,4.37,4.37,0,0,0-2.21.47,1.6,1.6,0,0,0-.79,1.48,1.58,1.58,0,0,0,.49,1.18,4.94,4.94,0,0,0,1.83.92L44.55,7a5.08,5.08,0,0,1,2.57,1.6A3.76,3.76,0,0,1,47.9,11a4.21,4.21,0,0,1-.44,1.93,4.4,4.4,0,0,1-1.21,1.47,5.43,5.43,0,0,1-1.85.93A8.25,8.25,0,0,1,42.05,15.62Z"></path><path class="cls-2" d="M45.19,23.81C39.72,27.85,31.78,30,25,30A36.64,36.64,0,0,1,.22,20.57c-.51-.46-.06-1.09.56-.74A49.78,49.78,0,0,0,25.53,26.4,49.23,49.23,0,0,0,44.4,22.53C45.32,22.14,46.1,23.14,45.19,23.81Z"></path><path class="cls-2" d="M47.47,21.21c-.7-.9-4.63-.42-6.39-.21-.53.06-.62-.4-.14-.74,3.13-2.2,8.27-1.57,8.86-.83s-.16,5.89-3.09,8.35c-.45.38-.88.18-.68-.32C46.69,25.8,48.17,22.11,47.47,21.21Z"></path></svg>

</a>

    </div>
    
    <div class="searchbox">
    <label for="search-by"><i class="fas fa-search"></i></label>
    <input data-search-input id="search-by" type="search" placeholder="">
    <span data-search-clear=""><i class="fas fa-times"></i></span>
</div>

<script type="text/javascript" src="/%20js/lunr.min.js?1751832716"></script>
<script type="text/javascript" src="/%20js/auto-complete.js?1751832716"></script>
<script type="text/javascript">
    { { if hugo.IsMultilingual } }
    var baseurl = "\/\/localhost:1313\/";
    { { else } }
    var baseurl = "\/\/localhost:1313\/";
    { { end } }
</script>
<script type="text/javascript" src="/%20js/search.js?1751832716"></script>
    
  </div>

  <div class="highlightable">
    <ul class="topics">

      
      
      







<li data-nav-id="/1-introduce/" title="Introduction" class="dd-item 
        
        
        
        ">
  <a href="/1-introduce/">
     <b> 1. </b> Introduction
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



      
      







<li data-nav-id="/2-prerequiste/" title="Preparation" class="dd-item 
        
        
        
        ">
  <a href="/2-prerequiste/">
     <b> 2 </b> Preparation
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
  <ul>
    
    
    
    
    

    
    
    
    







<li data-nav-id="/2-prerequiste/2.1-installnodejs/" title="Installing NodeJS" class="dd-item 
        
        
        
        ">
  <a href="/2-prerequiste/2.1-installnodejs/">
     <b> 2.1 </b> Installing NodeJS
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/2-prerequiste/2.2-installyarn/" title="Installing Yarn" class="dd-item 
        
        
        
        ">
  <a href="/2-prerequiste/2.2-installyarn/">
     <b> 2.2 </b> Installing Yarn
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/2-prerequiste/2.3-installsamcli/" title="Installing SAM CLI" class="dd-item 
        
        
        
        ">
  <a href="/2-prerequiste/2.3-installsamcli/">
     <b> 2.3 </b> Installing SAM CLI
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/2-prerequiste/2.4-createiam/" title="Create Account and Configure IAM" class="dd-item 
        
        
        
        ">
  <a href="/2-prerequiste/2.4-createiam/">
     <b> 2.4 </b> Create Account and Configure IAM
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/2-prerequiste/2.5-create-google-oauth2/" title="Create Google OAuth2 Project" class="dd-item 
        
        
        
        ">
  <a href="/2-prerequiste/2.5-create-google-oauth2/">
     <b> 2.5 </b> Create Google OAuth2 Project
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
  </ul>
  
</li>



      
      







<li data-nav-id="/3-deploybackend/" title="Deploying the Backend" class="dd-item 
        parent
        
        
        ">
  <a href="/3-deploybackend/">
     <b> 3. </b> Deploying the Backend
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
  <ul>
    
    
    
    
    

    
    
    
    







<li data-nav-id="/3-deploybackend/3.1-deploy-backend/" title="Deploying the Backend with SAM CLI/CloudFormation" class="dd-item 
        
        active
        
        ">
  <a href="/3-deploybackend/3.1-deploy-backend/">
     <b> 3.1 </b> Deploying the Backend with SAM CLI/CloudFormation
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/3-deploybackend/3.2-check-status-log-backend/" title="Checking Backend Status and Logs After Deployment" class="dd-item 
        
        
        
        ">
  <a href="/3-deploybackend/3.2-check-status-log-backend/">
     <b> 3.2 </b> Checking Backend Status and Logs After Deployment
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
  </ul>
  
</li>



      
      







<li data-nav-id="/4-testbackendapi/" title="Testing Backend APIs with Postman" class="dd-item 
        
        
        
        ">
  <a href="/4-testbackendapi/">
     <b> 4 </b> Testing Backend APIs with Postman
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
  <ul>
    
    
    
    
    

    
    
    
    







<li data-nav-id="/4-testbackendapi/4.1-endpoint-api-gateway/" title="Retrieving the API Gateway Endpoint" class="dd-item 
        
        
        
        ">
  <a href="/4-testbackendapi/4.1-endpoint-api-gateway/">
     <b> 4.1 </b> Retrieving the API Gateway Endpoint
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/4-testbackendapi/4.2-get-post-response/" title="Sending GET/POST Requests to Verify Backend Responses" class="dd-item 
        
        
        
        ">
  <a href="/4-testbackendapi/4.2-get-post-response/">
     <b> 4.2 </b> Sending GET/POST Requests to Verify Backend Responses
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
  </ul>
  
</li>



      
      







<li data-nav-id="/5-deployfrontend/" title="Deploy Frontend" class="dd-item 
        
        
        
        ">
  <a href="/5-deployfrontend/">
     <b> 5 </b> Deploy Frontend
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
  <ul>
    
    
    
    
    

    
    
    
    







<li data-nav-id="/5-deployfrontend/5.1-frontend-s3/" title="Deploy Frontend to S3 Bucket" class="dd-item 
        
        
        
        ">
  <a href="/5-deployfrontend/5.1-frontend-s3/">
     <b> 5.1 </b> Deploy Frontend to S3 Bucket
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/5-deployfrontend/5.2-enable-static-hosting/" title="Enable Static Hosting, Configure CORS, and Set S3 Bucket Permissions" class="dd-item 
        
        
        
        ">
  <a href="/5-deployfrontend/5.2-enable-static-hosting/">
     <b> 5.2 </b> Enable Static Hosting, Configure CORS, and Set S3 Bucket Permissions
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/5-deployfrontend/5.3-s3-bucket-permission/" title="Grant Public Permissions to S3 Bucket" class="dd-item 
        
        
        
        ">
  <a href="/5-deployfrontend/5.3-s3-bucket-permission/">
     <b> 5.3 </b> Grant Public Permissions to S3 Bucket
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/5-deployfrontend/5.4-clientid-clientserver/" title="Configure Google OAuth2 Client ID and Client Secret" class="dd-item 
        
        
        
        ">
  <a href="/5-deployfrontend/5.4-clientid-clientserver/">
     <b> 5.4 </b> Configure Google OAuth2 Client ID and Client Secret
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/5-deployfrontend/5.5-connect-frontend-api-backend/" title="Connect the Frontend to the Backend API" class="dd-item 
        
        
        
        ">
  <a href="/5-deployfrontend/5.5-connect-frontend-api-backend/">
     <b> 5.5 </b> Connect the Frontend to the Backend API
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
  </ul>
  
</li>



      
      







<li data-nav-id="/6-ssl-s3-static/" title="Setup SSL S3 Static Website" class="dd-item 
        
        
        
        ">
  <a href="/6-ssl-s3-static/">
    <b>6. </b>Setup SSL S3 Static Website
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



      
      







<li data-nav-id="/7-demo/" title="Demo and Run the Project" class="dd-item 
        
        
        
        ">
  <a href="/7-demo/">
    <b>7. </b>Demo and Run the Project
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



      
      







<li data-nav-id="/8-cleanup/" title="Clean Up Resources" class="dd-item 
        
        
        
        ">
  <a href="/8-cleanup/">
    <b>8. </b>Clean Up Resources
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



      
      
    </ul>

    
    
    <section id="shortcuts">
      <h3>More</h3>
      <ul>
        
        <li>
          <a class="padding" href="https://www.facebook.com/groups/awsstudygroupfcj/"><i class='fab fa-facebook'></i> AWS Study Group</a>
        </li>
        
      </ul>
    </section>
    

    
    <section id="prefooter">
      <hr />
      <ul>
        
        <li>
          <a class="padding">
            <i class="fas fa-language fa-fw"></i>
            <div class="select-style">
              <select id="select-language" onchange="location = this.value;">
                
                
                
                
                
                
                
                
                <option id="en" value="//localhost:1313/3-deploybackend/3.1-deploy-backend/" selected>English</option>
                
                
                
                
                
                
                
                
                
                
                
                
                
                <option id="vi" value="//localhost:1313/vi/3-deploybackend/3.1-deploy-backend/">Tiếng Việt
                </option>
                
                
                
                
              </select>
              <svg version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="255px" height="255px"
                viewBox="0 0 255 255" style="enable-background:new 0 0 255 255;" xml:space="preserve">
                <g>
                  <g id="arrow-drop-down">
                    <polygon points="0,63.75 127.5,191.25 255,63.75 		" />
                  </g>
                </g>
              </svg>
            </div>
          </a>
        </li>
        

        
        <li><a class="padding" href="#" data-clear-history-toggle=""><i class="fas fa-history fa-fw"></i> Clear History</a></li>
        
      </ul>
    </section>
    
    <section id="footer">
      <left>
    
     <b> Workshop</b> <br>
    <img src="https://hitwebcounter.com/counter/counter.php?page=7920860&style=0038&nbdigits=9&type=page&initCount=0" title="Migrate" Alt="web counter"   border="0" /></a>  <br>
     <b> <a href="https://cloudjourney.awsstudygroup.com/">Cloud Journey</a></b> <br>
    <img src="https://hitwebcounter.com/counter/counter.php?page=7830807&style=0038&nbdigits=9&type=page&initCount=0" title="Total CLoud Journey" Alt="web counter"   border="0"   />
     
</left>
<left>
    <br>
    <br>
        <b> Last Updated </b> <br>
        <i><font color=orange>30-01-2022</font></i>
    </left>
    <left>
        <br>
        <br>
            <b> Team </b> <br>
           
            <i> <a href="https://www.linkedin.com/in/sutrinh/"  style="color:orange">Sử Trịnh  </a> <br>
                <a href="https://www.linkedin.com/in/jotaguy"  style="color:orange">Gia Hưng </a> <br>
                <a href="https://www.linkedin.com/in/hiepnguyendt"  style="color:orange">Thanh Hiệp </a>
               
        </i>
        </left>

<script async defer src="https://buttons.github.io/buttons.js"></script>

    </section>
  </div>
</nav>



        <section id="body">
        <div id="overlay"></div>
        <div class="padding highlightable">
              
              <div>
                <div id="top-bar">
                
                
                <div id="breadcrumbs" itemscope="" itemtype="http://data-vocabulary.org/Breadcrumb">
                    <span id="sidebar-toggle-span">
                        <a href="#" id="sidebar-toggle" data-sidebar-toggle="">
                          <i class="fas fa-bars"></i>
                        </a>
                    </span>
                  
                  <span id="toc-menu"><i class="fas fa-list-alt"></i></span>
                  
                  <span class="links">
                 
                 
                    
          
          
            
            
          
          
            
            
          
          
            <a href='/'>Dynamic E-Commerce Website</a> > <a href='/3-deploybackend/'>Deploying the Backend</a> > Deploying the Backend with SAM CLI/CloudFormation
          
        
          
        
          
        
                 
                  </span>
                </div>
                
                    <div class="progress">
    <div class="wrapper">
<nav id="TableOfContents">
  <ul>
    <li>
      <ul>
        <li></li>
      </ul>
    </li>
  </ul>
</nav>
    </div>
</div>

                
              </div>
            </div>
            
        <div id="head-tags">
        
        </div>
        
        <div id="body-inner">
          
            <h1>
              
              Deploying the Backend with SAM CLI/CloudFormation
            </h1>
          

        



	<h4 id="deploying-the-backend-with-sam-clicloudformation">Deploying the Backend with SAM CLI/CloudFormation</h4>
<p>In this section, you will deploy the backend for your dynamic e-commerce website using <strong>SAM CLI</strong> and <strong>CloudFormation</strong>. We will use the provided <strong><code>template.yaml</code></strong> file in your project to automatically create resources such as <strong>API Gateway</strong>, <strong>Lambda</strong>, and <strong>DynamoDB</strong>.</p>
<h4 id="step-1-configure-aws-cli">Step 1: Configure AWS CLI</h4>
<p>Before using <strong>SAM CLI</strong> for deployment, you need to configure <strong>AWS CLI</strong> with your AWS account credentials. This allows <strong>SAM CLI</strong> to use the access rights granted to your IAM User.</p>
<ol>
<li>
<p><strong>Run the <code>aws configure</code> command</strong>:</p>
<ul>
<li>
<p>Open your terminal and enter the following command to configure AWS CLI:</p>
<pre><code> aws configure
</code></pre>
</li>
</ul>
</li>
<li>
<p><strong>Enter your configuration information</strong>:</p>
<ul>
<li>
<p><strong>AWS Access Key ID</strong>: Enter the <strong>Access Key ID</strong> generated when creating your IAM User.</p>
</li>
<li>
<p><strong>AWS Secret Access Key</strong>: Enter the <strong>Secret Access Key</strong> associated with your Access Key ID.</p>
</li>
<li>
<p><strong>Default region name</strong>: Enter <code>ap-southeast-1</code> (Singapore).</p>
</li>
<li>
<p><strong>Default output format</strong>: You can enter <code>json</code> or leave it as the default (<code>None</code>).
<img src="/images/aws_configure.png" alt="Error image"></p>
</li>
<li>
<p>Once finished, AWS CLI will save the configuration in the <code>~/.aws/credentials</code> file (Linux/macOS) or <code>C:\Users\<USER>\.aws\credentials</code> (Windows).</p>
</li>
</ul>
</li>
</ol>
<h4 id="step-2-deploy-the-backend-with-sam-cli">Step 2: Deploy the Backend with SAM CLI</h4>
<ol>
<li>
<p><strong>Check the <code>template.yaml</code> file</strong>:</p>
<ul>
<li>If you don’t have a <strong><code>template.yaml</code></strong> file, check your project directory. This file defines resources like <strong>API Gateway</strong>, <strong>Lambda</strong>, and <strong>DynamoDB</strong>.</li>
<li>If the file exists, make sure it is correctly configured for the resources you need.</li>
</ul>
</li>
<li>
<p><strong>Build the project</strong>:</p>
<ul>
<li>
<p>After ensuring that <strong><code>template.yaml</code></strong> is in your project,</p>
</li>
<li>
<p>Navigate to your backend project folder and use <strong>SAM CLI</strong> to build:</p>
<pre><code> sam build
</code></pre>
</li>
<li>
<p>If you see <strong>Build Succeeded</strong>, you have successfully installed and configured SAM CLI.
<img src="/images/build_success_samcli.png" alt="Error image"></p>
</li>
</ul>
</li>
<li>
<p><strong>Validate the Template</strong>:</p>
<ul>
<li>
<p>Check your <code>template.yaml</code> configuration to ensure there are no syntax or configuration errors:</p>
</li>
<li>
<p>Run <strong>sam validate</strong> to check your template:</p>
<pre><code> sam validate
</code></pre>
</li>
<li>
<p>If you see the message below, your <strong>template.yaml</strong> is valid.
<img src="/images/sam_validate.png" alt="Error image"></p>
</li>
</ul>
</li>
<li>
<p><strong>Deploy resources to AWS</strong>:</p>
<ul>
<li>
<p>After building and validating, deploy resources to AWS using:</p>
<pre><code> sam deploy --guided
</code></pre>
</li>
<li>
<p>When using <strong>SAM CLI</strong> to deploy, you’ll be prompted for configuration options. Suggested values:</p>
<ul>
<li><strong>Stack Name</strong>: <code>sam-app</code></li>
<li><strong>AWS Region</strong>: <code>ap-southeast-1</code></li>
<li><strong>Confirm changes before deploy</strong>: <code>y</code></li>
<li><strong>Allow SAM CLI IAM role creation</strong>: <code>y</code></li>
<li><strong>Disable rollback</strong>: <code>n</code> (do not disable rollback)</li>
<li><strong>ExpressApiFunction has no authentication. Is this okay?</strong>: <code>y</code></li>
<li><strong>Save arguments to configuration file</strong>: <code>y</code></li>
<li><strong>SAM configuration file</strong>: Press <strong>Enter</strong></li>
<li><strong>SAM configuration environment</strong>: Press <strong>Enter</strong></li>
</ul>
<p><img src="/images/deploy_this_changeset.png" alt="Error image"></p>
<ul>
<li><strong>Deploy this changeset</strong>: <code>y</code></li>
<li>Wait about 5 minutes for serverless resources (API Gateway, Lambda, DynamoDB, etc.) to be set up from your machine to AWS.</li>
</ul>
<p><img src="/images/create_sam_app_success.png" alt="Error image"></p>
</li>
</ul>
</li>
<li>
<p><strong>Verify Deployment</strong>:</p>
<ul>
<li>
<p>After successful deployment, SAM CLI will provide information about the resources created. You can check <strong>API Gateway</strong>, <strong>Lambda function</strong>, <strong>S3</strong>, <strong>CloudFormation</strong>, and <strong>DynamoDB</strong> in the AWS Management Console to confirm that everything has been set up correctly.

<div class="notices tip" ><p>Your backend has been successfully deployed! Resources like API Gateway, Lambda function, and DynamoDB have been automatically created. You can check them in the AWS Console.</p>
</div>
</p>
<ul>
<li>Go to <a href="https://ap-southeast-1.console.aws.amazon.com/apigateway/main/">API Gateway</a> to see your API Gateway.</li>
</ul>
<p><img src="/images/create_API_gateway.png" alt="Error image"></p>
<ul>
<li>Go to <a href="https://ap-southeast-1.console.aws.amazon.com/lambda/home/">Lambda function</a> to see your Lambda functions.</li>
</ul>
<p><img src="/images/create_auto_lambda.png" alt="Error image"></p>
<ul>
<li>Go to <a href="https://ap-southeast-1.console.aws.amazon.com/s3/home">Amazon S3</a> to see your S3 buckets.</li>
</ul>
<p><img src="/images/access_s3_after_setup_samcli.png" alt="Error image"></p>
<ul>
<li>Go to <a href="https://ap-southeast-1.console.aws.amazon.com/cloudformation/home">CloudFormation</a> to see your CloudFormation stacks.</li>
</ul>
<p><img src="/images/create_auto_cloud_foumation.png" alt="Error image"></p>
<ul>
<li>Go to <a href="https://ap-southeast-1.console.aws.amazon.com/dynamodbv2/home?region=ap-southeast-1#tables">DynamoDB</a> to see your DynamoDB tables.</li>
</ul>
<p><img src="/images/create_auto_dynamoDB.png" alt="Error image"></p>
</li>
</ul>
</li>
</ol>





<footer class=" footline" >
	
</footer>

        
        </div> 
        

      </div>

    <div id="navigation">
        
        
        
        
            
            
                
                    
                    
                
                

                    
                    
                        
                    
                    

                    
                        
            
            
                
                    
                        
                        
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                        
                    
                    

                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
            
        
                    
                        
            
            
                
                    
                    
                
                

                    
                    
                        
                    
                    

                    
                        
            
            
                
                    
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                        
                        
                    
                
                

                    
                    
                    

                    
            
        
                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                        
                    
                    

                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                        
                    
                    

                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
            
        
        
        


	 
	 
		
			<a class="nav nav-prev" href="/3-deploybackend/" title="Deploying the Backend"> <i class="fa fa-chevron-left"></i></a>
		
		
			<a class="nav nav-next" href="/3-deploybackend/3.2-check-status-log-backend/" title="Checking Backend Status and Logs After Deployment" style="margin-right: 0px;"><i class="fa fa-chevron-right"></i></a>
		
	
    </div>

    </section>
    
    <div style="left: -1000px; overflow: scroll; position: absolute; top: -1000px; border: none; box-sizing: content-box; height: 200px; margin: 0px; padding: 0px; width: 200px;">
      <div style="border: none; box-sizing: content-box; height: 200px; margin: 0px; padding: 0px; width: 200px;"></div>
    </div>
    <script src="/js/clipboard.min.js?1751832716"></script>
    <script src="/js/perfect-scrollbar.min.js?1751832716"></script>
    <script src="/js/perfect-scrollbar.jquery.min.js?1751832716"></script>
    <script src="/js/jquery.sticky.js?1751832716"></script>
    <script src="/js/featherlight.min.js?1751832716"></script>
    <script src="/js/highlight.pack.js?1751832716"></script>
    <script>hljs.initHighlightingOnLoad();</script>
    <script src="/js/modernizr.custom-3.6.0.js?1751832716"></script>
    <script src="/js/learn.js?1751832716"></script>
    <script src="/js/hugo-learn.js?1751832716"></script>

    <link href="/mermaid/mermaid.css?1751832716" rel="stylesheet" />
    <script src="/mermaid/mermaid.js?1751832716"></script>
    <script>
        mermaid.initialize({ startOnLoad: true });
    </script>
    <script>
  (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
  (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
  m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
  })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

  ga('create', 'UA-158079754-2', 'auto');
  ga('send', 'pageview');

</script>
  </body>
</html>
