/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 300px;
    background: #2c3e50;
    color: white;
    padding: 20px;
    overflow-y: auto;
    position: fixed;
    height: 100vh;
}

.logo h2 {
    color: white;
    margin-bottom: 30px;
    text-decoration: none;
}

.logo a {
    color: white;
    text-decoration: none;
}

.menu ul {
    list-style: none;
    margin-bottom: 20px;
}

.menu li {
    margin-bottom: 10px;
}

.menu a {
    color: #bdc3c7;
    text-decoration: none;
    display: block;
    padding: 8px 12px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.menu a:hover,
.menu .active a {
    background: #34495e;
    color: white;
}

.section-title a {
    font-weight: bold;
    color: #3498db !important;
}

.page-item {
    margin-left: 20px;
}

/* Content */
.content {
    flex: 1;
    margin-left: 300px;
    padding: 40px;
    max-width: calc(100% - 300px);
}

article {
    background: white;
    padding: 40px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    max-width: 800px;
}

h1, h2, h3, h4, h5, h6 {
    margin-bottom: 20px;
    color: #2c3e50;
}

h1 {
    font-size: 2.5em;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
}

h2 {
    font-size: 2em;
    margin-top: 30px;
}

h3 {
    font-size: 1.5em;
    margin-top: 25px;
}

p {
    margin-bottom: 15px;
}

ul, ol {
    margin-bottom: 15px;
    padding-left: 30px;
}

li {
    margin-bottom: 5px;
}

a {
    color: #3498db;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

img {
    max-width: 100%;
    height: auto;
    margin: 20px 0;
    border-radius: 4px;
}

/* Notice boxes */
.notice {
    display: flex;
    margin: 20px 0;
    padding: 15px;
    border-radius: 4px;
    border-left: 4px solid;
}

.notice-icon {
    margin-right: 15px;
    font-size: 1.2em;
}

.notice-content {
    flex: 1;
}

.notice-info {
    background: #e8f4fd;
    border-color: #3498db;
    color: #2980b9;
}

.notice-warning {
    background: #fef9e7;
    border-color: #f39c12;
    color: #d68910;
}

.notice-note {
    background: #f0f3ff;
    border-color: #6c5ce7;
    color: #5a4fcf;
}

.notice-tip {
    background: #e8f8f5;
    border-color: #00b894;
    color: #00a085;
}

/* Page navigation */
.page-nav {
    display: flex;
    justify-content: space-between;
    margin-top: 40px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.page-nav a {
    padding: 10px 15px;
    background: #3498db;
    color: white;
    border-radius: 4px;
    text-decoration: none;
    transition: background 0.3s ease;
}

.page-nav a:hover {
    background: #2980b9;
}

.next-page {
    margin-left: auto;
}

/* Page list */
.page-list {
    list-style: none;
    padding: 0;
}

.page-list li {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 4px;
}

.page-list a {
    font-weight: bold;
    font-size: 1.1em;
}

.page-list p {
    margin-top: 5px;
    color: #666;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        position: relative;
        height: auto;
    }
    
    .content {
        margin-left: 0;
        max-width: 100%;
        padding: 20px;
    }
    
    .container {
        flex-direction: column;
    }
}
