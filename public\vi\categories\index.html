<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Categories :: AWS System Manager</title>
    <link rel="stylesheet" href="/workshopFCJ/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <nav class="sidebar">
            <div class="logo">
    <a href="https://thuananwork.github.io/workshopFCJ/">
        <h2>AWS System Manager</h2>
    </a>
</div>

<nav class="menu">
    <ul>
        
    </ul>
    
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/1-introduce/">
            <strong>false. Giới thiệu</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/">
            <strong>false. Chuẩn bị môi trường</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/2-prerequiste/2.1-installnodejs/">
            false. Cài đặt NodeJS
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/2-prerequiste/2.2-installyarn/">
            false. Cài đặt Yarn
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/2-prerequiste/2.3-installsamcli/">
            false. Cài đặt SAM CLI
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/2-prerequiste/2.4-createiam/">
            false. Tạo tài khoản và cấu hình IAM
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/2-prerequiste/2.5-create-google-oauth2/">
            false. Tạo Google OAuth2 Project
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/2.1-installnodejs/">
            <strong>false. Cài đặt NodeJS</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/2.2-installyarn/">
            <strong>false. Cài đặt Yarn</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/2.3-installsamcli/">
            <strong>false. Cài đặt SAM CLI</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/2.4-createiam/">
            <strong>false. Tạo tài khoản và cấu hình IAM</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/2.5-create-google-oauth2/">
            <strong>false. Tạo Google OAuth2 Project</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/3-deploybackend/">
            <strong>false. Triển khai backend</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/3-deploybackend/3.1-deploy-backend/">
            false. Triển khai backend bằng SAM CLI/CloudFormation
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/3-deploybackend/3.2-check-status-log-backend/">
            false. Kiểm tra trạng thái và log backend sau khi triển khai
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/3-deploybackend/3.1-deploy-backend/">
            <strong>false. Triển khai backend bằng SAM CLI/CloudFormation</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/3-deploybackend/3.2-check-status-log-backend/">
            <strong>false. Kiểm tra trạng thái và log backend sau khi triển khai</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/4-testbackendapi/">
            <strong>false. Kiểm thử API backend với Postman</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/4-testbackendapi/4.1-endpoint-api-gateway/">
            false. Lấy endpoint API Gateway
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/4-testbackendapi/4.2-get-post-response/">
            false. Gửi request GET/POST để kiểm tra response backend
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/4-testbackendapi/4.1-endpoint-api-gateway/">
            <strong>false. Lấy endpoint API Gateway</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/4-testbackendapi/4.2-get-post-response/">
            <strong>false. Gửi request GET/POST để kiểm tra response backend</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/">
            <strong>false. Triển khai frontend</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.1-frontend-s3/">
            false. Triển khai frontend lên S3 Bucket
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.2-enable-static-hosting/">
            false. Bật static hosting, cấu hình CORS
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.3-s3-bucket-permission/">
            false. Cấp quyền public cho S3 Bucket
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.4-clientid-clientserver/">
            false. Cấu hình Google OAuth2 Client ID và Client Secret
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.5-connect-frontend-api-backend/">
            false. Kết nối frontend với API backend
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.1-frontend-s3/">
            <strong>false. Triển khai frontend lên S3 Bucket</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.2-enable-static-hosting/">
            <strong>false. Bật static hosting, cấu hình CORS</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.3-s3-bucket-permission/">
            <strong>false. Cấp quyền public cho S3 Bucket</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.4-clientid-clientserver/">
            <strong>false. Cấu hình Google OAuth2 Client ID và Client Secret</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.5-connect-frontend-api-backend/">
            <strong>false. Kết nối frontend với API backend</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/6-ssl-s3-static/">
            <strong>false. Thiết lập trang web SSL S3 Static</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/7-demo/">
            <strong>false. Demo và chạy dự án</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/8-cleanup/">
            <strong>false. Dọn dẹp tài nguyên</strong>
        </a>
    </li>
    
    
</ul>

    
</nav>



        </nav>
        <main class="content">
            
<article>
    <header>
        <h1>Categories</h1>
    </header>
    <div class="article-content">
        
        
    </div>
</article>

        </main>
    </div>
</body>
</html>
