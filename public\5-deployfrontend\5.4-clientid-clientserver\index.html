<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configure Google OAuth2 Client ID and Client Secret :: AWS System Manager</title>
    <link rel="stylesheet" href="/workshopFCJ/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <nav class="sidebar">
            <div class="logo">
    <a href="https://thuananwork.github.io/workshopFCJ/">
        <h2>AWS System Manager</h2>
    </a>
</div>

<nav class="menu">
    <ul>
        
    </ul>
    
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/1-introduce/">
            <strong>false. Introduction</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/">
            <strong>false. Preparation</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.1-installnodejs/">
            false. Installing NodeJS
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.2-installyarn/">
            false. Installing Yarn
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.3-installsamcli/">
            false. Installing SAM CLI
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.4-createiam/">
            false. Create Account and Configure IAM
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.5-create-google-oauth2/">
            false. Create Google OAuth2 Project
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.1-installnodejs/">
            <strong>false. Installing NodeJS</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.2-installyarn/">
            <strong>false. Installing Yarn</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.3-installsamcli/">
            <strong>false. Installing SAM CLI</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.4-createiam/">
            <strong>false. Create Account and Configure IAM</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.5-create-google-oauth2/">
            <strong>false. Create Google OAuth2 Project</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/3-deploybackend/">
            <strong>false. Deploying the Backend</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/3-deploybackend/3.1-deploy-backend/">
            false. Deploying the Backend with SAM CLI/CloudFormation
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/3-deploybackend/3.2-check-status-log-backend/">
            false. Checking Backend Status and Logs After Deployment
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/3-deploybackend/3.1-deploy-backend/">
            <strong>false. Deploying the Backend with SAM CLI/CloudFormation</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/3-deploybackend/3.2-check-status-log-backend/">
            <strong>false. Checking Backend Status and Logs After Deployment</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/4-testbackendapi/">
            <strong>false. Testing Backend APIs with Postman</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/4-testbackendapi/4.1-endpoint-api-gateway/">
            false. Retrieving the API Gateway Endpoint
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/4-testbackendapi/4.2-get-post-response/">
            false. Sending GET/POST Requests to Verify Backend Responses
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/4-testbackendapi/4.1-endpoint-api-gateway/">
            <strong>false. Retrieving the API Gateway Endpoint</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/4-testbackendapi/4.2-get-post-response/">
            <strong>false. Sending GET/POST Requests to Verify Backend Responses</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/">
            <strong>false. Deploy Frontend</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.1-frontend-s3/">
            false. Deploy Frontend to S3 Bucket
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.2-enable-static-hosting/">
            false. Enable Static Hosting, Configure CORS, and Set S3 Bucket Permissions
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.3-s3-bucket-permission/">
            false. Grant Public Permissions to S3 Bucket
        </a>
    </li>
    
    <li class="page-item active">
        <a href="/workshopFCJ/5-deployfrontend/5.4-clientid-clientserver/">
            false. Configure Google OAuth2 Client ID and Client Secret
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.5-connect-frontend-api-backend/">
            false. Connect the Frontend to the Backend API
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.1-frontend-s3/">
            <strong>false. Deploy Frontend to S3 Bucket</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.2-enable-static-hosting/">
            <strong>false. Enable Static Hosting, Configure CORS, and Set S3 Bucket Permissions</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.3-s3-bucket-permission/">
            <strong>false. Grant Public Permissions to S3 Bucket</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.4-clientid-clientserver/">
            <strong>false. Configure Google OAuth2 Client ID and Client Secret</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.5-connect-frontend-api-backend/">
            <strong>false. Connect the Frontend to the Backend API</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/6-ssl-s3-static/">
            <strong>false. Setup SSL S3 Static Website</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/7-demo/">
            <strong>false. Demo and Run the Project</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/8-cleanup/">
            <strong>false. Clean Up Resources</strong>
        </a>
    </li>
    
    
</ul>

    
</nav>



        </nav>
        <main class="content">
            
<article>
    <header>
        <h1>Configure Google OAuth2 Client ID and Client Secret</h1>
    </header>
    <div class="article-content">
        <h4 id="configure-google-oauth2-client-id-and-client-secret">Configure Google OAuth2 Client ID and Client Secret</h4>
<p>After creating the Google OAuth2 Project and enabling the required APIs during the environment preparation step, you need to create an <strong>OAuth2 Client ID and Client Secret</strong> to integrate Google Sign-In into your e-commerce system.</p>
<p><strong>Steps to follow:</strong></p>
<ol>
<li>
<p><strong>Access Google Cloud Console</strong></p>
<ul>
<li>Open <a href="https://console.cloud.google.com/">Google Cloud Console</a> and select the correct project you created earlier.
<img src="/images/api_services.png" alt="api_services"></li>
</ul>
</li>
<li>
<p><strong>Create OAuth2 Client ID</strong></p>
<ul>
<li>
<p>Navigate to <strong>APIs &amp; Services</strong> → <strong>Credentials</strong>.
<img src="/images/credentials.png" alt="credentials"></p>
</li>
<li>
<p>Click <strong>+ Create Credentials</strong> → <strong>OAuth client ID</strong>.
<img src="/images/oAuth_client_id.png" alt="create_oAuth_client_id"></p>
</li>
<li>
<p>Click <strong>Configure consent screen</strong>.
<img src="/images/configure_consent_screen.png" alt="configure_consent_screen"></p>
</li>
<li>
<p>Click <strong>Get Started</strong>.
<img src="/images/get_started.png" alt="get_started"></p>
</li>
<li>
<p>Fill in the following information:</p>
<ul>
<li>
<p>App name: <code>FcjFashionShop</code></p>
</li>
<li>
<p>User support email: <strong>Enter your email</strong></p>
</li>
<li>
<p>Click <strong>Next</strong>
<img src="/images/information_google_oauth.png" alt="information_google_oauth"></p>
</li>
<li>
<p>Email addresses: <strong>Enter your email</strong></p>
</li>
<li>
<p>Click <strong>Next</strong>
<img src="/images/contact_information.png" alt="contact_information"></p>
</li>
<li>
<p>Check <strong>I agree to the Google API Services: User Data Policy.</strong></p>
</li>
<li>
<p>Click <strong>Continue</strong> and then <strong>Create</strong>
<img src="/images/finish_create_oauth.png" alt="finish_create_oauth"></p>
</li>
</ul>
</li>
<li>
<p>In <strong>Metrics</strong>, click <strong>Create OAuth client</strong>
<img src="/images/select_create_oauth_client.png" alt="select_create_oauth_client"></p>
</li>
<li>
<p>For Application type, select <strong>Web application</strong></p>
</li>
<li>
<p>Name: <code>FcjFashionShop</code>
<img src="/images/info_oauth_client_id.png" alt="info_oauth_client_id"></p>
</li>
<li>
<p>In <strong>Authorized JavaScript origins</strong></p>
<ul>
<li>Click <strong>Add URI</strong> to add a new URL</li>
<li>Paste your <strong>S3 Bucket website endpoint</strong> (the static website URL you copied earlier)</li>
</ul>
</li>
<li>
<p>In <strong>Authorized redirect URIs</strong></p>
<ul>
<li>Click <strong>Add URI</strong> to add a new URL</li>
<li>Paste the <strong>Invoke URL of your API Gateway</strong> that you copied earlier, replacing <strong>your-API-Gateway-domain</strong> in the example below</li>
<li>Click <strong>Create</strong></li>
</ul>
<pre tabindex="0"><code>your-API-Gateway-domain/api/users/auth/google/callback
</code></pre></li>
</ul>
<p><img src="/images/create_oauth_client_id.png" alt="create_oauth_client_id"></p>
<ul>
<li><strong>ClientID</strong> and <strong>ClientSecret</strong> have been created successfully. Copy and save them for later use.
<img src="/images/success_clientid_client_secret.png" alt="success_clientid_client_secret"></li>
</ul>
</li>
</ol>
<div class="notice notice-info">
    <div class="notice-icon">
<pre><code>        &lt;i class=&quot;fas fa-info-circle&quot;&gt;&lt;/i&gt;
    
&lt;/div&gt;
&lt;div class=&quot;notice-content&quot;&gt;
    Authorized JavaScript origins: this is your frontend domain (the S3 Static Website endpoint).&lt;br&gt;
</code></pre>
<p>Authorized redirect URIs: this is the backend endpoint (API Gateway) that handles the Google callback.
</div></p>
</div>
<div class="notice notice-warning">
    <div class="notice-icon">
<pre><code>        &lt;i class=&quot;fas fa-exclamation-triangle&quot;&gt;&lt;/i&gt;
    
&lt;/div&gt;
&lt;div class=&quot;notice-content&quot;&gt;
    &lt;strong&gt;Security note:&lt;/strong&gt;&lt;br&gt;
</code></pre>
<p>Never publish your <strong>Client Secret</strong> on Github or anywhere public!
</div></p>
</div>
<p><strong>Conclusion:</strong><br>
After completing these steps, you have all the necessary information to configure Google OAuth2 for both backend and frontend, and are ready to implement Google Sign-In for your e-commerce website project on AWS.</p>

        
    </div>
</article>

        </main>
    </div>
</body>
</html>
