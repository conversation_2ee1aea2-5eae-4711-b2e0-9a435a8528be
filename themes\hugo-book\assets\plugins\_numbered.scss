$startLevel: 1;
$endLevel: 6;

.book-page .markdown.book-article {
  @for $currentLevel from $startLevel through $endLevel {
    h#{$currentLevel} {
      counter-increment: h#{$currentLevel};
      counter-reset: h#{$currentLevel + 1};

      $content: "";
      @for $n from $startLevel through $currentLevel {
        $content: $content + 'counter(h#{$n})"."';
      }

      &::before {
        content: unquote($content) " ";
      }
    }
  }
}

.book-toc nav#TableOfContents ul {
  counter-reset: item;

  li {
    counter-increment: item;

    &:before {
      content: counters(item, ".") ". ";
      float: left;
      margin-inline-end: $padding-4;
    }
  }
}
