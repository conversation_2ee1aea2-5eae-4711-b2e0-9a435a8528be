<!DOCTYPE html>
<html lang="en" class="js csstransforms3d">
  <head><script src="/YOUR_REPOSITORY/livereload.js?mindelay=10&amp;v=2&amp;port=1313&amp;path=YOUR_REPOSITORY/livereload" data-no-instant defer></script>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="generator" content="Hugo 0.147.8">
    <meta name="description" content="">
<meta name="author" content="<EMAIL>">

    <link rel="icon" href="/YOUR_REPOSITORY/images/favicon.png" type="image/png">

    <title>Installing Yarn :: AWS System Manager</title>

    
    <link href="/YOUR_REPOSITORY/css/nucleus.css?1751832823" rel="stylesheet">
    <link href="/YOUR_REPOSITORY/css/fontawesome-all.min.css?1751832823" rel="stylesheet">
    <link href="/YOUR_REPOSITORY/css/hybrid.css?1751832823" rel="stylesheet">
    <link href="/YOUR_REPOSITORY/css/featherlight.min.css?1751832823" rel="stylesheet">
    <link href="/YOUR_REPOSITORY/css/perfect-scrollbar.min.css?1751832823" rel="stylesheet">
    <link href="/YOUR_REPOSITORY/css/auto-complete.css?1751832823" rel="stylesheet">
    <link href="/YOUR_REPOSITORY/css/atom-one-dark-reasonable.css?1751832823" rel="stylesheet">
    <link href="/YOUR_REPOSITORY/css/theme.css?1751832823" rel="stylesheet">
    <link href="/YOUR_REPOSITORY/css/hugo-theme.css?1751832823" rel="stylesheet">
    
    <link href="/YOUR_REPOSITORY/css/theme-workshop.css?1751832823" rel="stylesheet">
    
    

    <script src="/YOUR_REPOSITORY/js/jquery-3.3.1.min.js?1751832823"></script>

    <style>
      :root #header + #content > #left > #rlblock_left{
          display:none !important;
      }
      
    </style>
    
  </head>
  <body class="" data-url="/YOUR_REPOSITORY/2-prerequiste/2.2-installyarn/">
    <nav id="sidebar" class="showVisitedLinks">

  
  
  <div id="header-wrapper">
    <div id="header">
      <a id="logo" href="/">

<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 30" width="30%"><defs><style>.cls-1{fill:#fff;}.cls-2{fill:#f90;fill-rule:evenodd;}</style></defs><title>AWS-Logo_White-Color</title><path class="cls-1" d="M14.09,10.85a4.7,4.7,0,0,0,.19,1.48,7.73,7.73,0,0,0,.54,**********,0,0,1,.***********,0,0,1-.32.49l-1,.7a.83.83,0,0,1-.***********,0,0,1-.49-.23,3.8,3.8,0,0,1-.6-.77q-.25-.42-.51-1a6.14,6.14,0,0,1-4.89,2.3,4.54,4.54,0,0,1-3.32-1.19,4.27,4.27,0,0,1-1.22-3.2A4.28,4.28,0,0,1,3.61,7.75,6.06,6.06,0,0,1,7.69,6.46a12.47,12.47,0,0,1,1.76.13q.92.13,1.91.36V5.73a3.65,3.65,0,0,0-.79-2.66A3.81,3.81,0,0,0,7.86,2.3a7.71,7.71,0,0,0-1.79.22,12.78,12.78,0,0,0-1.79.57,4.55,4.55,0,0,1-.58.22l-.26,0q-.35,0-.35-.52V2a1.09,1.09,0,0,1,.12-.58,1.2,1.2,0,0,1,.47-.35A10.88,10.88,0,0,1,5.77.32,10.19,10.19,0,0,1,8.36,0a6,6,0,0,1,4.35,1.35,5.49,5.49,0,0,1,1.38,4.09ZM7.34,13.38a5.36,5.36,0,0,0,1.72-.31A3.63,3.63,0,0,0,10.63,12,2.62,2.62,0,0,0,11.19,11a5.63,5.63,0,0,0,.16-1.44v-.7a14.35,14.35,0,0,0-1.53-.28,12.37,12.37,0,0,0-1.56-.1,3.84,3.84,0,0,0-2.47.67A2.34,2.34,0,0,0,5,11a2.35,2.35,0,0,0,.61,1.76A2.4,2.4,0,0,0,7.34,13.38Zm13.35,1.8a1,1,0,0,1-.64-.16,1.3,1.3,0,0,1-.35-.65L15.81,1.51a3,3,0,0,1-.15-.67.36.36,0,0,1,.41-.41H17.7a1,1,0,0,1,.65.16,1.4,1.4,0,0,1,.33.65l2.79,11,2.59-11A1.17,1.17,0,0,1,24.39.6a1.1,1.1,0,0,1,.67-.16H26.4a1.1,1.1,0,0,1,.67.16,1.17,1.17,0,0,1,.32.65L30,12.39,32.88,1.25A1.39,1.39,0,0,1,33.22.6a1,1,0,0,1,.65-.16h1.54a.36.36,0,0,1,.41.41,1.36,1.36,0,0,1,0,.26,3.64,3.64,0,0,1-.12.41l-4,12.86a1.3,1.3,0,0,1-.35.65,1,1,0,0,1-.64.16H29.25a1,1,0,0,1-.67-.17,1.26,1.26,0,0,1-.32-.67L25.67,3.64,23.11,14.34a1.26,1.26,0,0,1-.32.67,1,1,0,0,1-.67.17Zm21.36.44a11.28,11.28,0,0,1-2.56-.29,7.44,7.44,0,0,1-1.92-.67,1,1,0,0,1-.61-.93v-.84q0-.52.38-.52a.9.9,0,0,1,.31.06l.42.17a8.77,8.77,0,0,0,1.83.58,9.78,9.78,0,0,0,2,.2,4.48,4.48,0,0,0,2.43-.55,1.76,1.76,0,0,0,.86-1.57,1.61,1.61,0,0,0-.45-1.16A4.29,4.29,0,0,0,43,9.22l-2.41-.76A5.15,5.15,0,0,1,38,6.78a3.94,3.94,0,0,1-.83-2.41,3.7,3.7,0,0,1,.45-1.85,4.47,4.47,0,0,1,1.19-1.37A5.27,5.27,0,0,1,40.51.29,7.4,7.4,0,0,1,42.6,0a8.87,8.87,0,0,1,1.12.07q.57.07,1.08.19t.95.26a4.27,4.27,0,0,1,.7.29,1.59,1.59,0,0,1,.49.41.94.94,0,0,1,.15.55v.79q0,.52-.38.52a1.76,1.76,0,0,1-.64-.2,7.74,7.74,0,0,0-3.2-.64,4.37,4.37,0,0,0-2.21.47,1.6,1.6,0,0,0-.79,1.48,1.58,1.58,0,0,0,.49,1.18,4.94,4.94,0,0,0,1.83.92L44.55,7a5.08,5.08,0,0,1,2.57,1.6A3.76,3.76,0,0,1,47.9,11a4.21,4.21,0,0,1-.44,1.93,4.4,4.4,0,0,1-1.21,1.47,5.43,5.43,0,0,1-1.85.93A8.25,8.25,0,0,1,42.05,15.62Z"></path><path class="cls-2" d="M45.19,23.81C39.72,27.85,31.78,30,25,30A36.64,36.64,0,0,1,.22,20.57c-.51-.46-.06-1.09.56-.74A49.78,49.78,0,0,0,25.53,26.4,49.23,49.23,0,0,0,44.4,22.53C45.32,22.14,46.1,23.14,45.19,23.81Z"></path><path class="cls-2" d="M47.47,21.21c-.7-.9-4.63-.42-6.39-.21-.53.06-.62-.4-.14-.74,3.13-2.2,8.27-1.57,8.86-.83s-.16,5.89-3.09,8.35c-.45.38-.88.18-.68-.32C46.69,25.8,48.17,22.11,47.47,21.21Z"></path></svg>

</a>

    </div>
    
    <div class="searchbox">
    <label for="search-by"><i class="fas fa-search"></i></label>
    <input data-search-input id="search-by" type="search" placeholder="">
    <span data-search-clear=""><i class="fas fa-times"></i></span>
</div>

<script type="text/javascript" src="/YOUR_REPOSITORY/%20js/lunr.min.js?1751832823"></script>
<script type="text/javascript" src="/YOUR_REPOSITORY/%20js/auto-complete.js?1751832823"></script>
<script type="text/javascript">
    { { if hugo.IsMultilingual } }
    var baseurl = "http:\/\/localhost:1313\/YOUR_REPOSITORY\/";
    { { else } }
    var baseurl = "http:\/\/localhost:1313\/YOUR_REPOSITORY\/";
    { { end } }
</script>
<script type="text/javascript" src="/YOUR_REPOSITORY/%20js/search.js?1751832823"></script>
    
  </div>

  <div class="highlightable">
    <ul class="topics">

      
      
      







<li data-nav-id="/YOUR_REPOSITORY/1-introduce/" title="Introduction" class="dd-item 
        
        
        
        ">
  <a href="/YOUR_REPOSITORY/1-introduce/">
     <b> 1. </b> Introduction
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



      
      







<li data-nav-id="/YOUR_REPOSITORY/2-prerequiste/" title="Preparation" class="dd-item 
        parent
        
        
        ">
  <a href="/YOUR_REPOSITORY/2-prerequiste/">
     <b> 2 </b> Preparation
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
  <ul>
    
    
    
    
    

    
    
    
    







<li data-nav-id="/YOUR_REPOSITORY/2-prerequiste/2.1-installnodejs/" title="Installing NodeJS" class="dd-item 
        
        
        
        ">
  <a href="/YOUR_REPOSITORY/2-prerequiste/2.1-installnodejs/">
     <b> 2.1 </b> Installing NodeJS
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/YOUR_REPOSITORY/2-prerequiste/2.2-installyarn/" title="Installing Yarn" class="dd-item 
        
        active
        
        ">
  <a href="/YOUR_REPOSITORY/2-prerequiste/2.2-installyarn/">
     <b> 2.2 </b> Installing Yarn
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/YOUR_REPOSITORY/2-prerequiste/2.3-installsamcli/" title="Installing SAM CLI" class="dd-item 
        
        
        
        ">
  <a href="/YOUR_REPOSITORY/2-prerequiste/2.3-installsamcli/">
     <b> 2.3 </b> Installing SAM CLI
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/YOUR_REPOSITORY/2-prerequiste/2.4-createiam/" title="Create Account and Configure IAM" class="dd-item 
        
        
        
        ">
  <a href="/YOUR_REPOSITORY/2-prerequiste/2.4-createiam/">
     <b> 2.4 </b> Create Account and Configure IAM
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/YOUR_REPOSITORY/2-prerequiste/2.5-create-google-oauth2/" title="Create Google OAuth2 Project" class="dd-item 
        
        
        
        ">
  <a href="/YOUR_REPOSITORY/2-prerequiste/2.5-create-google-oauth2/">
     <b> 2.5 </b> Create Google OAuth2 Project
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
  </ul>
  
</li>



      
      







<li data-nav-id="/YOUR_REPOSITORY/3-deploybackend/" title="Deploying the Backend" class="dd-item 
        
        
        
        ">
  <a href="/YOUR_REPOSITORY/3-deploybackend/">
     <b> 3. </b> Deploying the Backend
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
  <ul>
    
    
    
    
    

    
    
    
    







<li data-nav-id="/YOUR_REPOSITORY/3-deploybackend/3.1-deploy-backend/" title="Deploying the Backend with SAM CLI/CloudFormation" class="dd-item 
        
        
        
        ">
  <a href="/YOUR_REPOSITORY/3-deploybackend/3.1-deploy-backend/">
     <b> 3.1 </b> Deploying the Backend with SAM CLI/CloudFormation
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/YOUR_REPOSITORY/3-deploybackend/3.2-check-status-log-backend/" title="Checking Backend Status and Logs After Deployment" class="dd-item 
        
        
        
        ">
  <a href="/YOUR_REPOSITORY/3-deploybackend/3.2-check-status-log-backend/">
     <b> 3.2 </b> Checking Backend Status and Logs After Deployment
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
  </ul>
  
</li>



      
      







<li data-nav-id="/YOUR_REPOSITORY/4-testbackendapi/" title="Testing Backend APIs with Postman" class="dd-item 
        
        
        
        ">
  <a href="/YOUR_REPOSITORY/4-testbackendapi/">
     <b> 4 </b> Testing Backend APIs with Postman
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
  <ul>
    
    
    
    
    

    
    
    
    







<li data-nav-id="/YOUR_REPOSITORY/4-testbackendapi/4.1-endpoint-api-gateway/" title="Retrieving the API Gateway Endpoint" class="dd-item 
        
        
        
        ">
  <a href="/YOUR_REPOSITORY/4-testbackendapi/4.1-endpoint-api-gateway/">
     <b> 4.1 </b> Retrieving the API Gateway Endpoint
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/YOUR_REPOSITORY/4-testbackendapi/4.2-get-post-response/" title="Sending GET/POST Requests to Verify Backend Responses" class="dd-item 
        
        
        
        ">
  <a href="/YOUR_REPOSITORY/4-testbackendapi/4.2-get-post-response/">
     <b> 4.2 </b> Sending GET/POST Requests to Verify Backend Responses
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
  </ul>
  
</li>



      
      







<li data-nav-id="/YOUR_REPOSITORY/5-deployfrontend/" title="Deploy Frontend" class="dd-item 
        
        
        
        ">
  <a href="/YOUR_REPOSITORY/5-deployfrontend/">
     <b> 5 </b> Deploy Frontend
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
  <ul>
    
    
    
    
    

    
    
    
    







<li data-nav-id="/YOUR_REPOSITORY/5-deployfrontend/5.1-frontend-s3/" title="Deploy Frontend to S3 Bucket" class="dd-item 
        
        
        
        ">
  <a href="/YOUR_REPOSITORY/5-deployfrontend/5.1-frontend-s3/">
     <b> 5.1 </b> Deploy Frontend to S3 Bucket
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/YOUR_REPOSITORY/5-deployfrontend/5.2-enable-static-hosting/" title="Enable Static Hosting, Configure CORS, and Set S3 Bucket Permissions" class="dd-item 
        
        
        
        ">
  <a href="/YOUR_REPOSITORY/5-deployfrontend/5.2-enable-static-hosting/">
     <b> 5.2 </b> Enable Static Hosting, Configure CORS, and Set S3 Bucket Permissions
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/YOUR_REPOSITORY/5-deployfrontend/5.3-s3-bucket-permission/" title="Grant Public Permissions to S3 Bucket" class="dd-item 
        
        
        
        ">
  <a href="/YOUR_REPOSITORY/5-deployfrontend/5.3-s3-bucket-permission/">
     <b> 5.3 </b> Grant Public Permissions to S3 Bucket
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/YOUR_REPOSITORY/5-deployfrontend/5.4-clientid-clientserver/" title="Configure Google OAuth2 Client ID and Client Secret" class="dd-item 
        
        
        
        ">
  <a href="/YOUR_REPOSITORY/5-deployfrontend/5.4-clientid-clientserver/">
     <b> 5.4 </b> Configure Google OAuth2 Client ID and Client Secret
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/YOUR_REPOSITORY/5-deployfrontend/5.5-connect-frontend-api-backend/" title="Connect the Frontend to the Backend API" class="dd-item 
        
        
        
        ">
  <a href="/YOUR_REPOSITORY/5-deployfrontend/5.5-connect-frontend-api-backend/">
     <b> 5.5 </b> Connect the Frontend to the Backend API
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
  </ul>
  
</li>



      
      







<li data-nav-id="/YOUR_REPOSITORY/6-ssl-s3-static/" title="Setup SSL S3 Static Website" class="dd-item 
        
        
        
        ">
  <a href="/YOUR_REPOSITORY/6-ssl-s3-static/">
    <b>6. </b>Setup SSL S3 Static Website
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



      
      







<li data-nav-id="/YOUR_REPOSITORY/7-demo/" title="Demo and Run the Project" class="dd-item 
        
        
        
        ">
  <a href="/YOUR_REPOSITORY/7-demo/">
    <b>7. </b>Demo and Run the Project
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



      
      







<li data-nav-id="/YOUR_REPOSITORY/8-cleanup/" title="Clean Up Resources" class="dd-item 
        
        
        
        ">
  <a href="/YOUR_REPOSITORY/8-cleanup/">
    <b>8. </b>Clean Up Resources
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



      
      
    </ul>

    
    
    <section id="shortcuts">
      <h3>More</h3>
      <ul>
        
        <li>
          <a class="padding" href="https://www.facebook.com/groups/awsstudygroupfcj/"><i class='fab fa-facebook'></i> AWS Study Group</a>
        </li>
        
      </ul>
    </section>
    

    
    <section id="prefooter">
      <hr />
      <ul>
        
        <li>
          <a class="padding">
            <i class="fas fa-language fa-fw"></i>
            <div class="select-style">
              <select id="select-language" onchange="location = this.value;">
                
                
                
                
                
                
                
                
                <option id="en" value="http://localhost:1313/YOUR_REPOSITORY/2-prerequiste/2.2-installyarn/" selected>English</option>
                
                
                
                
                
                
                
                
                
                
                
                
                
                <option id="vi" value="http://localhost:1313/YOUR_REPOSITORY/vi/2-prerequiste/2.2-installyarn/">Tiếng Việt
                </option>
                
                
                
                
              </select>
              <svg version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="255px" height="255px"
                viewBox="0 0 255 255" style="enable-background:new 0 0 255 255;" xml:space="preserve">
                <g>
                  <g id="arrow-drop-down">
                    <polygon points="0,63.75 127.5,191.25 255,63.75 		" />
                  </g>
                </g>
              </svg>
            </div>
          </a>
        </li>
        

        
        <li><a class="padding" href="#" data-clear-history-toggle=""><i class="fas fa-history fa-fw"></i> Clear History</a></li>
        
      </ul>
    </section>
    
    <section id="footer">
      <left>
    
     <b> Workshop</b> <br>
    <img src="https://hitwebcounter.com/counter/counter.php?page=7920860&style=0038&nbdigits=9&type=page&initCount=0" title="Migrate" Alt="web counter"   border="0" /></a>  <br>
     <b> <a href="https://cloudjourney.awsstudygroup.com/">Cloud Journey</a></b> <br>
    <img src="https://hitwebcounter.com/counter/counter.php?page=7830807&style=0038&nbdigits=9&type=page&initCount=0" title="Total CLoud Journey" Alt="web counter"   border="0"   />
     
</left>
<left>
    <br>
    <br>
        <b> Last Updated </b> <br>
        <i><font color=orange>30-01-2022</font></i>
    </left>
    <left>
        <br>
        <br>
            <b> Team </b> <br>
           
            <i> <a href="https://www.linkedin.com/in/sutrinh/"  style="color:orange">Sử Trịnh  </a> <br>
                <a href="https://www.linkedin.com/in/jotaguy"  style="color:orange">Gia Hưng </a> <br>
                <a href="https://www.linkedin.com/in/hiepnguyendt"  style="color:orange">Thanh Hiệp </a>
               
        </i>
        </left>

<script async defer src="https://buttons.github.io/buttons.js"></script>

    </section>
  </div>
</nav>



        <section id="body">
        <div id="overlay"></div>
        <div class="padding highlightable">
              
              <div>
                <div id="top-bar">
                
                
                <div id="breadcrumbs" itemscope="" itemtype="http://data-vocabulary.org/Breadcrumb">
                    <span id="sidebar-toggle-span">
                        <a href="#" id="sidebar-toggle" data-sidebar-toggle="">
                          <i class="fas fa-bars"></i>
                        </a>
                    </span>
                  
                  <span id="toc-menu"><i class="fas fa-list-alt"></i></span>
                  
                  <span class="links">
                 
                 
                    
          
          
            
            
          
          
            
            
          
          
            <a href='/YOUR_REPOSITORY/'>Dynamic E-Commerce Website</a> > <a href='/YOUR_REPOSITORY/2-prerequiste/'>Preparation</a> > Installing Yarn
          
        
          
        
          
        
                 
                  </span>
                </div>
                
                    <div class="progress">
    <div class="wrapper">
<nav id="TableOfContents">
  <ul>
    <li>
      <ul>
        <li></li>
      </ul>
    </li>
  </ul>
</nav>
    </div>
</div>

                
              </div>
            </div>
            
        <div id="head-tags">
        
        </div>
        
        <div id="body-inner">
          
            <h1>
              
              Installing Yarn
            </h1>
          

        



	<p>Yarn is a package manager for JavaScript that is widely used in frontend projects to efficiently manage libraries and software packages. Yarn helps address issues related to installation speed, consistency, and security when managing project dependencies.</p>
<h4 id="step-1-install-yarn">Step 1: Install Yarn</h4>
<ol>
<li>
<p><strong>Install Yarn via npm:</strong>+</p>
<ul>
<li>
<p>Yarn can be installed through npm (Node Package Manager). Make sure you have <strong>NodeJS</strong> and <strong>npm</strong> installed before proceeding.</p>
</li>
<li>
<p>To install Yarn, open your terminal or Command Prompt and run the following command:</p>
<pre><code>npm install -g yarn
</code></pre>
</li>
</ul>
</li>
<li>
<p><strong>Install Yarn on macOS (via Homebrew):</strong></p>
<ul>
<li>
<p>If you are using macOS, you can install Yarn via <strong>Homebrew</strong> with the following command:</p>
<pre><code>brew install yarn
</code></pre>
</li>
</ul>
</li>
<li>
<p><strong>Install Yarn on Linux (Ubuntu):</strong></p>
<ul>
<li>
<p>To install Yarn on Ubuntu, you need to add the Yarn repository to your system and run the following command:</p>
<pre><code>sudo apt update &amp;&amp; sudo apt install yarn
</code></pre>
</li>
</ul>

<div class="notices success" ><p>Yarn has been installed successfully! You can now start managing frontend libraries for your project.</p>
</div>

</li>
</ol>
<h4 id="step-2-verify-yarn-installation">Step 2: Verify Yarn Installation</h4>
<ol>
<li>
<p><strong>Check Yarn version:</strong></p>
<ul>
<li>
<p>After successfully installing Yarn, you can verify the version by running:</p>
<pre><code>yarn --version
</code></pre>
</li>
<li>
<p>If the version number is displayed, Yarn has been installed successfully:</p>
<pre><code>C:\Users\<USER>