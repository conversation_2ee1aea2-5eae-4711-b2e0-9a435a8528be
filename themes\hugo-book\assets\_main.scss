html {
  font-size: $font-size-base;
  scroll-behavior: smooth;
  touch-action: manipulation;
  scrollbar-gutter: stable;
}

body {
  min-width: $body-min-width;
  color: var(--body-font-color);
  background: var(--body-background);

  // letter-spacing: 0.33px;
  font-weight: $body-font-weight;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  box-sizing: border-box;
  * {
    box-sizing: inherit;
  }
}

h1,
h2,
h3,
h4,
h5 {
  font-weight: $body-font-weight;
}

a {
  text-decoration: none;
  color: var(--color-link);
}

img {
  vertical-align: baseline;
}

:focus {
  @include outline;
}

aside nav ul {
  padding: 0;
  margin: 0;
  list-style: none;

  li {
    position: relative;
  }

  a, span {
    padding: .5em 0;
    display: block;
  }

  a:hover {
    opacity: 0.5;
  }

  ul {
    padding-inline-start: $padding-16;
  }
}

ul.pagination {
  display: flex;
  justify-content: center;
  list-style-type: none;
  padding-inline-start: 0px;

  .page-item a {
    padding: $padding-16;
  }
}

.container {
  max-width: $container-max-width;
  margin: 0 auto;
}

.book-icon {
  filter: var(--icon-filter);
}

a .book-icon {
  height: 1em;
  width: 1em;
  margin-inline-end: .5em;
}

.book-brand {
  margin-top: 0;
  margin-bottom: $padding-16;

  img {
    height: 1.5em;
    width: 1.5em;
    margin-inline-end: $padding-8;
  }
}

.book-menu {
  flex: 0 0 $menu-width;
  font-size: $font-size-14;

  .book-menu-content {
    width: $menu-width;
    padding: $padding-16;
    background: var(--body-background);

    @include fixed;
  }

  a,
  label {
    color: inherit;
    cursor: pointer;
    word-wrap: break-word;
  }

  a.active {
    color: var(--color-link);
  }

  input.toggle + label + ul {
    display: none;
  }

  input.toggle:checked + label + ul {
    display: block;
  }

  input.toggle + label::after {
    content: "▸";
    align-self: center;
  }

  input.toggle:checked + label::after {
    content: "▾";
    align-self: center;
  }
}

// for RTL support
body[dir="rtl"] .book-menu {
  input.toggle + label::after {
    content: "◂";
  }

  input.toggle:checked + label::after {
    content: "▾";
  }
}

.book-section-flat {
  margin: $padding-16 0;

  > a,
  > span,
  > label {
    font-weight: bolder;
  }

  > ul {
    padding-inline-start: 0;
  }
}

.book-page {
  min-width: $body-min-width;
  flex-grow: 1;
  padding: $padding-16;
}

.book-post {
  margin-bottom: $padding-16 * 4;

  .book-post-date img {
    height: 1em;
    width: 1em;
    margin-inline-end: .5em;
  }

  .book-post-content > :first-child {
    margin-top: $padding-16;
  }
}

.book-header {
  display: none;
  margin-bottom: $padding-16;

  label {
    line-height: 0;
  }

  h3 {
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 0 $padding-16;
  }

  img.book-icon {
    height: 1.5em;
    width: 1.5em;
  }
}

.book-search {
  position: relative;
  margin: $padding-8 0;
  border-bottom: 1px solid transparent;

  input {
    width: 100%;
    padding: $padding-8;

    border: 0;
    border-radius: $border-radius;

    background: var(--gray-100);
    color: var(--body-font-color);

    &:required + .book-search-spinner {
      display: block;
    }
  }

  .book-search-spinner {
    position: absolute;
    top: 0;
    margin: $padding-8;
    margin-inline-start: calc(100% - #{$padding-16 + $padding-8});

    width: $padding-16;
    height: $padding-16;

    border: $padding-1 solid transparent;
    border-top-color: var(--body-font-color);
    border-radius: 50%;

    @include spin(1s);
  }

  ul a {
    padding-bottom: 0;
  }

  small {
    opacity: 0.5;
  }
}

.book-toc {
  flex: 0 0 $toc-width;
  font-size: $font-size-12;

  .book-toc-content {
    width: $toc-width;
    padding: $padding-16;

    @include fixed;
  }

  img {
    height: 1em;
    width: 1em;
  }

  nav > ul > li:first-child {
    margin-top: 0;
  }
}

.book-footer {
  padding-top: $padding-16;
  font-size: $font-size-14;
}

.book-comments {
  margin-top: $padding-16;
}

.book-languages {
  margin-bottom: $padding-16;

  ul {
    padding-inline-start: 1.5em;
  }
}

// Responsive styles
.book-menu-content,
.book-toc-content,
.book-page,
.book-header aside,
.markdown {
  transition: 0.2s ease-in-out;
  transition-property: transform, margin, opacity, visibility;
  will-change: transform, margin, opacity;
}

@media screen and (max-width: $mobile-breakpoint) {
  #menu-control,
  #toc-control {
    display: inline;
  }

  .book-menu {
    visibility: hidden;
    margin-inline-start: -$menu-width;
    z-index: 1;
  }

  .book-toc {
    display: none;
  }

  .book-header {
    display: block;
  }

  #menu-control:focus ~ main label[for="menu-control"] {
    @include outline;
  }

  #menu-control:checked ~ main {
    .book-menu {
      visibility: initial;
    }

    .book-menu .book-menu-content {
      transform: translateX($menu-width);
      box-shadow: 0 0 $padding-8 rgba(0, 0, 0, 0.1);
    }

    .book-page {
      opacity: 0.25;
    }

    .book-menu-overlay {
      display: block;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
    }
  }

  #toc-control:focus ~ main label[for="toc-control"] {
    @include outline;
  }

  #toc-control:checked ~ main {
    .book-header aside {
      display: block;
    }
  }

  // for RTL support
  body[dir="rtl"] #menu-control:checked ~ main {
    .book-menu .book-menu-content {
      transform: translateX(-$menu-width);
    }
  }
}

// Extra space for big screens
@media screen and (min-width: $container-max-width) {
  .book-page,
  .book-menu .book-menu-content,
  .book-toc .book-toc-content {
    padding: $padding-16 * 2 $padding-16;
  }
}
