# theme.toml template for a Hugo theme
# See https://github.com/spf13/hugoThemes#themetoml for an example

name = "Learn"
license = "MIT"
licenselink = "https://github.com/matcornic/hugo-theme-learn/blob/master/LICENSE.md"
description = "Documentation theme for <PERSON>, based on Grav Learn theme"
homepage = "https://github.com/matcornic/hugo-theme-learn/"
repo = "https://github.com/matcornic/hugo-theme-learn"
tags = ["documentation", "grav", "learn", "doc", "search"]
features = ["documentation", "menu", "nested sections", "search", "mermaid"]
min_version = 0.25

[author]
  name = "Mathieu Cornic"
  homepage = "https://matcornic.github.io/"

[original]
  name = "Grav Learn"
  homepage = "https://learn.getgrav.org/"
  repo = "https://github.com/getgrav/grav-learn"
