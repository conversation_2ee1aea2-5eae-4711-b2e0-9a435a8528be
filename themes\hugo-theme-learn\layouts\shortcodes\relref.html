{{- if in (.Get 0) "/_index.md" -}}
	{{- $paths := (split (.Get 0) "_index.md") -}}
	{{- $pagepath := index $paths 0 -}}
	{{- $anchor := index $paths 1 -}}
	{{- with .Site.GetPage "section" (trim $pagepath "/") -}}
		{{- ( printf "%s%s" $pagepath $anchor ) | relLangURL -}}
	{{- end -}}
{{- else -}}
	{{- with .Site.GetPage "section" (.Get 0) }}
		{{- .RelPermalink -}}
	{{- else -}}
		{{- .Get 0 | relref .Page -}}
	{{- end -}}
{{- end -}}