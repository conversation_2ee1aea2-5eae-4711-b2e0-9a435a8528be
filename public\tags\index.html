<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tags :: AWS System Manager</title>
    <link rel="stylesheet" href="/workshopFCJ/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <nav class="sidebar">
            <div class="logo">
    <a href="https://thuananwork.github.io/workshopFCJ/">
        <h2>AWS System Manager</h2>
    </a>
</div>

<nav class="menu">
    <ul>
        
    </ul>
    
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/1-introduce/">
            <strong>false. Introduction</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/">
            <strong>false. Preparation</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.1-installnodejs/">
            false. Installing NodeJS
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.2-installyarn/">
            false. Installing Yarn
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.3-installsamcli/">
            false. Installing SAM CLI
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.4-createiam/">
            false. Create Account and Configure IAM
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.5-create-google-oauth2/">
            false. Create Google OAuth2 Project
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.1-installnodejs/">
            <strong>false. Installing NodeJS</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.2-installyarn/">
            <strong>false. Installing Yarn</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.3-installsamcli/">
            <strong>false. Installing SAM CLI</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.4-createiam/">
            <strong>false. Create Account and Configure IAM</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.5-create-google-oauth2/">
            <strong>false. Create Google OAuth2 Project</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/3-deploybackend/">
            <strong>false. Deploying the Backend</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/3-deploybackend/3.1-deploy-backend/">
            false. Deploying the Backend with SAM CLI/CloudFormation
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/3-deploybackend/3.2-check-status-log-backend/">
            false. Checking Backend Status and Logs After Deployment
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/3-deploybackend/3.1-deploy-backend/">
            <strong>false. Deploying the Backend with SAM CLI/CloudFormation</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/3-deploybackend/3.2-check-status-log-backend/">
            <strong>false. Checking Backend Status and Logs After Deployment</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/4-testbackendapi/">
            <strong>false. Testing Backend APIs with Postman</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/4-testbackendapi/4.1-endpoint-api-gateway/">
            false. Retrieving the API Gateway Endpoint
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/4-testbackendapi/4.2-get-post-response/">
            false. Sending GET/POST Requests to Verify Backend Responses
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/4-testbackendapi/4.1-endpoint-api-gateway/">
            <strong>false. Retrieving the API Gateway Endpoint</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/4-testbackendapi/4.2-get-post-response/">
            <strong>false. Sending GET/POST Requests to Verify Backend Responses</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/">
            <strong>false. Deploy Frontend</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.1-frontend-s3/">
            false. Deploy Frontend to S3 Bucket
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.2-enable-static-hosting/">
            false. Enable Static Hosting, Configure CORS, and Set S3 Bucket Permissions
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.3-s3-bucket-permission/">
            false. Grant Public Permissions to S3 Bucket
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.4-clientid-clientserver/">
            false. Configure Google OAuth2 Client ID and Client Secret
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.5-connect-frontend-api-backend/">
            false. Connect the Frontend to the Backend API
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.1-frontend-s3/">
            <strong>false. Deploy Frontend to S3 Bucket</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.2-enable-static-hosting/">
            <strong>false. Enable Static Hosting, Configure CORS, and Set S3 Bucket Permissions</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.3-s3-bucket-permission/">
            <strong>false. Grant Public Permissions to S3 Bucket</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.4-clientid-clientserver/">
            <strong>false. Configure Google OAuth2 Client ID and Client Secret</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.5-connect-frontend-api-backend/">
            <strong>false. Connect the Frontend to the Backend API</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/6-ssl-s3-static/">
            <strong>false. Setup SSL S3 Static Website</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/7-demo/">
            <strong>false. Demo and Run the Project</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/8-cleanup/">
            <strong>false. Clean Up Resources</strong>
        </a>
    </li>
    
    
</ul>

    
</nav>



        </nav>
        <main class="content">
            
<article>
    <header>
        <h1>Tags</h1>
    </header>
    <div class="article-content">
        
        
    </div>
</article>

        </main>
    </div>
</body>
</html>
