# Changelog

## v2.5.0 (01/06/2020)

#### New features

- [**feature**] Allow custom css files to be loaded by configuration [#376](https://github.com/matcornic/hugo-theme-learn/pull/376)
- [**feature**] Added flag to turn off image featherlight [#333](https://github.com/matcornic/hugo-theme-learn/pull/333)

#### Bug Fixes

- [**bug**] fix null pointer when no params on image url [#383](https://github.com/matcornic/hugo-theme-learn/pull/383)
- [**bug**] can't give argument to non-function not .Params.hidden [#403](https://github.com/matcornic/hugo-theme-learn/pull/403)
- [**bug**] Fix .File.UniqueID on zero object warning [#372](https://github.com/matcornic/hugo-theme-learn/pull/372)
- [**bug**] fix select option color in Chrome [#345](https://github.com/matcornic/hugo-theme-learn/pull/345)
- [**bug**] remove unneeded spaces from HTML [#320](https://github.com/matcornic/hugo-theme-learn/pull/320)
- [**bug**] Avoid displaying hidden sections in menu [#354](https://github.com/matcornic/hugo-theme-learn/pull/354)

#### Enhancements

- [**enhancement**] Updated theme installation documentation link. [#390](https://github.com/matcornic/hugo-theme-learn/pull/390)
- [**enhancement**] [ImgBot] Optimize images [#366](https://github.com/matcornic/hugo-theme-learn/pull/366)
- [**enhancement**] fix extra spaces in the description [#339](https://github.com/matcornic/hugo-theme-learn/pull/339)
- [**enhancement**] change to minified 'autocomplete.js' for improved performance [#321](https://github.com/matcornic/hugo-theme-learn/pull/321)

#### Internationalisation

- [**i18n**] Fix: typos [#343](https://github.com/matcornic/hugo-theme-learn/pull/343)
- [**i18n**] Fix: typo [#342](https://github.com/matcornic/hugo-theme-learn/pull/342)
- [**i18n**] Incorrect english in notice documentation [#336](https://github.com/matcornic/hugo-theme-learn/pull/336)
- [**i18n**] Add Chinese language for this theme [#349](https://github.com/matcornic/hugo-theme-learn/pull/349)
- [**i18n**] Add jp and zh cn [#335](https://github.com/matcornic/hugo-theme-learn/pull/335)
- [**i18n**] Added Russian translation [#332](https://github.com/matcornic/hugo-theme-learn/pull/332)

#### Theme Meta

- [**meta**] Fix various linting warnings [#405](https://github.com/matcornic/hugo-theme-learn/pull/405)
- [**meta**] Netlify build on 0.66.0 [#377](https://github.com/matcornic/hugo-theme-learn/pull/377)
- [**meta**] add inteliver documentation showcase [#331](https://github.com/matcornic/hugo-theme-learn/pull/331)

---

## v2.4.0 (04/09/2019)

#### Bug Fixes

- [**bug**] correct GitHub capitalization [#293](https://github.com/matcornic/hugo-theme-learn/pull/293)
- [**bug**] fix 'locate' typo [#285](https://github.com/matcornic/hugo-theme-learn/pull/285)
- [**bug**] Remove reference to the unused horsey CSS [#284](https://github.com/matcornic/hugo-theme-learn/pull/284)
- [**bug**] Fix invalid HTML in 404 template. [#271](https://github.com/matcornic/hugo-theme-learn/pull/271)

#### Enhancements

- [**enhancement**] Delete html5shiv-printshiv.min.js [#319](https://github.com/matcornic/hugo-theme-learn/pull/319)
- [**enhancement**] remove html5shiv [#315](https://github.com/matcornic/hugo-theme-learn/pull/315)
- [**enhancement**] Fix deprecated .Hugo, .UniqueID and .URL references [#303](https://github.com/matcornic/hugo-theme-learn/pull/303)
- [**enhancement**] Fix type lines 364 and 369 [#304](https://github.com/matcornic/hugo-theme-learn/pull/304)
- [**enhancement**] remove unneeded type="text/css" [#298](https://github.com/matcornic/hugo-theme-learn/pull/298)
- [**enhancement**] remove unneeded type="text/css" [#297](https://github.com/matcornic/hugo-theme-learn/pull/297)
- [**enhancement**] HTTPS links in footer [#295](https://github.com/matcornic/hugo-theme-learn/pull/295)
- [**enhancement**] remove unneeded  type="text/css" [#292](https://github.com/matcornic/hugo-theme-learn/pull/292)
- [**enhancement**] Make shortcodes & example site compatible with Hugo v0.55 [#281](https://github.com/matcornic/hugo-theme-learn/pull/281)

#### Internationalisation

- [**i18n**] Added German and Arabic translation [#312](https://github.com/matcornic/hugo-theme-learn/pull/312)

#### Theme Meta

- [**meta**] Added Tshark.dev as example site [#318](https://github.com/matcornic/hugo-theme-learn/pull/318)

---

## v2.3.0 (16/04/2019)

#### New features

- [**feature**] Added support for tags [#196](https://github.com/matcornic/hugo-theme-learn/pull/196)

#### Bug Fixes

- [**bug**] Fix issue where "children" shortcode only shows top level. [#252](https://github.com/matcornic/hugo-theme-learn/pull/252)
- [**bug**] Fix translation when using a custom baseURL [#234](https://github.com/matcornic/hugo-theme-learn/pull/234)
- [**bug**] Preventing left/right arrow key navigation in textareas [#241](https://github.com/matcornic/hugo-theme-learn/pull/241)
- [**bug**] Update menu.html to include the check icon [#229](https://github.com/matcornic/hugo-theme-learn/pull/229)

#### Enhancements

- [**enhancement**] Render the ```mermaid blocks into graphs [#226](https://github.com/matcornic/hugo-theme-learn/pull/226)
- [**enhancement**] Remove oudated versions of jquery & modernizr libraries [#259](https://github.com/matcornic/hugo-theme-learn/pull/259)
- [**enhancement**] Various Updates [#237](https://github.com/matcornic/hugo-theme-learn/pull/237)
- [**enhancement**] Use style instead of width and height in logo [#250](https://github.com/matcornic/hugo-theme-learn/pull/250)
- [**enhancement**] [ImgBot] Optimize images [#222](https://github.com/matcornic/hugo-theme-learn/pull/222)
- [**enhancement**] remove duplicate icon and fix incorrect favicon type [#227](https://github.com/matcornic/hugo-theme-learn/pull/227)
- [**enhancement**] HTTPS links in exampleSite menu-footer [#223](https://github.com/matcornic/hugo-theme-learn/pull/223)

#### Internationalisation

- [**i18n**] Dutch i18n [#239](https://github.com/matcornic/hugo-theme-learn/pull/239)

#### Theme Meta

- [**meta**] Add .editorconfig [#224](https://github.com/matcornic/hugo-theme-learn/pull/224)

---

## v2.2.0 (28/01/2019)

#### New features

- [**feature**] Sitewide param to enable or disable next/previous page buttons and breadcrumbs [#184](https://github.com/matcornic/hugo-theme-learn/pull/184)

#### Bug Fixes

- [**bug**] Fix baseurl used by search to load json data [#177](https://github.com/matcornic/hugo-theme-learn/pull/177)
- [**bug**] Updated CSS link to fontawesome library [#186](https://github.com/matcornic/hugo-theme-learn/pull/186)
- [**bug**] Close / Cancel search icon not showing in input box [#215](https://github.com/matcornic/hugo-theme-learn/pull/215)
- [**bug**] Prevent left and right keydown events while in input fields [#219](https://github.com/matcornic/hugo-theme-learn/pull/219)
- [**bug**] xss fix [#182](https://github.com/matcornic/hugo-theme-learn/pull/182)
- [**bug**] Fix error in blockquote documentation fixes #165 [#190](https://github.com/matcornic/hugo-theme-learn/pull/190)

#### Enhancements

- [**enhancement**] Update mermaid.js to a499296 [#199](https://github.com/matcornic/hugo-theme-learn/pull/199)
- [**enhancement**] Update Font Awesome to 5.0.6 [#129](https://github.com/matcornic/hugo-theme-learn/pull/129)
- [**enhancement**] Update 404.html alttext [#161](https://github.com/matcornic/hugo-theme-learn/pull/161)
- [**enhancement**] Remove CSS source map metadata [#167](https://github.com/matcornic/hugo-theme-learn/pull/167)
- [**enhancement**] Load github images in examplesite via https instead of http [#180](https://github.com/matcornic/hugo-theme-learn/pull/180)
- [**enhancement**] Load main site logo via BaseUrl [#185](https://github.com/matcornic/hugo-theme-learn/pull/185)
- [**enhancement**] HTTPS links in examplesite sidebar [#200](https://github.com/matcornic/hugo-theme-learn/pull/200)
- [**enhancement**] Use correct input type for search [#205](https://github.com/matcornic/hugo-theme-learn/pull/205)
- [**enhancement**] HTTPS link to learn.getgrav.org [#207](https://github.com/matcornic/hugo-theme-learn/pull/207)
- [**enhancement**] Update html5shiv-printshiv.min.js [#208](https://github.com/matcornic/hugo-theme-learn/pull/208)
- [**enhancement**] Remove whitespace from clippy.svg [#211](https://github.com/matcornic/hugo-theme-learn/pull/211)
- [**enhancement**] Upgrade fontawesome to 5.6.3 [#218](https://github.com/matcornic/hugo-theme-learn/pull/218)
- [**enhancement**] fix clickable nodes style in mermaid [#169](https://github.com/matcornic/hugo-theme-learn/pull/169)

#### Internationalisation

- [**i18n**] French language correction [#157](https://github.com/matcornic/hugo-theme-learn/pull/157)
- [**i18n**] French language correction [#158](https://github.com/matcornic/hugo-theme-learn/pull/158)
- [**i18n**] Add indonesian translation [#159](https://github.com/matcornic/hugo-theme-learn/pull/159)
- [**i18n**] Add Turkish i18n config file [#175](https://github.com/matcornic/hugo-theme-learn/pull/175)

#### Theme Meta

- [**meta**] Fix wercker builds [#178](https://github.com/matcornic/hugo-theme-learn/pull/178)
- [**meta**] Declare netlify buildsteps in repo file rather than in webui [#217](https://github.com/matcornic/hugo-theme-learn/pull/217)

---

## v2.1.0 (10/08/2018)

#### Internationalisation

- [**i18n**] Clean up the English language phrasing [#146](https://github.com/matcornic/hugo-theme-learn/pull/146)
- [**i18n**] Updated _index.en.md for typo "names" [#150](https://github.com/matcornic/hugo-theme-learn/pull/150)

#### Theme Meta

- [**meta**] Add license scan report and status [#151](https://github.com/matcornic/hugo-theme-learn/pull/151)

#### Uncategorised

- [**closed**] Different viewport [#122](https://github.com/matcornic/hugo-theme-learn/pull/122)
- [**closed**] TranslationBaseName replaced for Name on archetypes template [#145](https://github.com/matcornic/hugo-theme-learn/pull/145)
- [**closed**] Improved variant of #119, as per request [#124](https://github.com/matcornic/hugo-theme-learn/pull/124)
- [**closed**] role="" is invalid [#121](https://github.com/matcornic/hugo-theme-learn/pull/121)
- [**closed**] Related to previous PR [#120](https://github.com/matcornic/hugo-theme-learn/pull/120)
- [**closed**] Issue #111 for _index.fr.md [#117](https://github.com/matcornic/hugo-theme-learn/pull/117)
- [**closed**] Issue #111 for _index.en.md [#116](https://github.com/matcornic/hugo-theme-learn/pull/116)
- [**closed**] Issue #111 for theme-green.css [#114](https://github.com/matcornic/hugo-theme-learn/pull/114)
- [**closed**] Issue #111 for theme-red.css [#113](https://github.com/matcornic/hugo-theme-learn/pull/113)
- [**closed**] Issue #111 for theme-blue.css [#112](https://github.com/matcornic/hugo-theme-learn/pull/112)
- [**closed**] Issue #111 for theme-mine.css [#115](https://github.com/matcornic/hugo-theme-learn/pull/115)
- [**closed**] fix #77 : no wrap images that already wrapped [#118](https://github.com/matcornic/hugo-theme-learn/pull/118)
- [**closed**] doc: Keep icons aligned [#110](https://github.com/matcornic/hugo-theme-learn/pull/110)
- [**closed**] Add Portuguese translation [#109](https://github.com/matcornic/hugo-theme-learn/pull/109)
- [**closed**] Use `relURL`  where possible [#102](https://github.com/matcornic/hugo-theme-learn/pull/102)
- [**closed**] Bug fix in sidebar menu and children description generation [#105](https://github.com/matcornic/hugo-theme-learn/pull/105)
- [**closed**] fix some typo [#104](https://github.com/matcornic/hugo-theme-learn/pull/104)
- [**closed**] Added a `menuTitle` attribute to partials/menu.html [#90](https://github.com/matcornic/hugo-theme-learn/pull/90)
- [**closed**] allowing comments system [#86](https://github.com/matcornic/hugo-theme-learn/pull/86)
- [**closed**] Add spanish translation [#85](https://github.com/matcornic/hugo-theme-learn/pull/85)
- [**closed**] Replace horsey with Pixabay's autocomplete [#75](https://github.com/matcornic/hugo-theme-learn/pull/75)
- [**closed**] Added info about 'draft:true' [#74](https://github.com/matcornic/hugo-theme-learn/pull/74)
- [**closed**] Remove white space from `align` parameter [#63](https://github.com/matcornic/hugo-theme-learn/pull/63)

---

## v2.0.0 (20/08/2017)

#### Uncategorised

- [**closed**] V2 [#56](https://github.com/matcornic/hugo-theme-learn/pull/56)

---

## v1.1.0 (22/07/2017)

#### Bug Fixes

- [**bug**] Fix sticky header jumpiness [#45](https://github.com/matcornic/hugo-theme-learn/pull/45)

#### Uncategorised

- [**closed**] Fix anchor scrolling that hides behind top nav bar [#46](https://github.com/matcornic/hugo-theme-learn/pull/46)
- [**closed**] feat: Change the default code color [#43](https://github.com/matcornic/hugo-theme-learn/pull/43)
- [**closed**] Use index pages + automatic navigation arrows [#36](https://github.com/matcornic/hugo-theme-learn/pull/36)

---

## v1.0.0 (25/03/2017)

#### Uncategorised

- [**closed**] Search [#18](https://github.com/matcornic/hugo-theme-learn/pull/18)
- [**closed**] Extracted menu footer content to separate partial file [#35](https://github.com/matcornic/hugo-theme-learn/pull/35)
- [**closed**] feat: style Hugo figure shortcode [#33](https://github.com/matcornic/hugo-theme-learn/pull/33)
- [**closed**] Fix URL for 'Edit this page' on Windows [#27](https://github.com/matcornic/hugo-theme-learn/pull/27)
- [**closed**] Revert "Menu generation using Content file instead directories" [#10](https://github.com/matcornic/hugo-theme-learn/pull/10)
- [**closed**] (#25) use .Site.BaseURL to lcoate static assets [#26](https://github.com/matcornic/hugo-theme-learn/pull/26)
- [**closed**] Menu ordering and definition in config.toml [#8](https://github.com/matcornic/hugo-theme-learn/pull/8)
- [**closed**] Menu generation using Content file instead directories [#5](https://github.com/matcornic/hugo-theme-learn/pull/5)
- [**closed**] Add Checkmark On visited Links [#4](https://github.com/matcornic/hugo-theme-learn/pull/4)
- [**closed**] doc: Add theme installation instructions [#1](https://github.com/matcornic/hugo-theme-learn/pull/1)
