// Used in layout
$padding-1: 1px !default;
$padding-4: 0.25rem !default;
$padding-8: 0.5rem !default;
$padding-16: 1rem !default;

$font-size-base: 16px !default;
$font-size-12: 0.75rem !default;
$font-size-14: 0.875rem !default;
$font-size-16: 1rem !default;

$border-radius: $padding-4 !default;

$body-font-weight: normal !default;

$body-min-width: 20rem !default;
$container-max-width: 80rem !default;

$menu-width: 16rem !default;
$toc-width: 16rem !default;

$mobile-breakpoint: $menu-width + $body-min-width * 1.2 + $toc-width !default;

$hint-colors: (
  info: #6bf,
  warning: #fd6,
  danger: #f66,
) !default;

// Themes
@mixin theme-light {
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-500: #adb5bd;

  --color-link: #0055bb;
  --color-visited-link: #8440f1;

  --body-background: white;
  --body-font-color: black;

  --icon-filter: none;

  --hint-color-info: #6bf;
  --hint-color-warning: #fd6;
  --hint-color-danger: #f66;
}

@mixin theme-dark {
  --gray-100: #494e54;
  --gray-200: #5c6165;
  --gray-500: #999d9f;

  --color-link: #84b2ff;
  --color-visited-link: #b88dff;

  --body-background: #343a40;
  --body-font-color: #e9ecef;

  --icon-filter: brightness(0) invert(1);

  --hint-color-info: #6bf;
  --hint-color-warning: #fd6;
  --hint-color-danger: #f66;
}
