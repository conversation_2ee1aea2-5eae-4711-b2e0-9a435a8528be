# theme.toml template for a Hugo theme
# See https://github.com/gohugoio/hugoThemes#themetoml for an example

name = "Book"
license = "MIT"
licenselink = "https://github.com/alex-shpak/hugo-book/blob/master/LICENSE"
description = "Hugo documentation theme as simple as plain book"
homepage = "https://github.com/alex-shpak/hugo-book"
demosite = "https://hugo-book-demo.netlify.app"
tags = ["responsive", "clean", "documentation", "docs", "flexbox", "search", "mobile", "multilingual", "disqus"]
features = []
min_version = "0.128.0"

[author]
  name = "<PERSON> Shp<PERSON>"
  homepage = "https://github.com/alex-shpak/"
