<!DOCTYPE html>
<html lang="{{ .Page.Language | default "en" }}" class="js csstransforms3d">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    {{ hugo.Generator }}
    {{ partial "meta.html" . }}
    {{ partial "favicon.html" . }}
    <title>{{ .Title }} {{ default "::" .Site.Params.titleSeparator }} {{ .Site.Title }}</title>

    {{ $assetBusting := not .Site.Params.disableAssetsBusting }}
    <link href="{{"css/nucleus.css" | relURL}}{{ if $assetBusting }}?{{ now.Unix }}{{ end }}" rel="stylesheet">
    <link href="{{"css/fontawesome-all.min.css" | relURL}}{{ if $assetBusting }}?{{ now.Unix }}{{ end }}" rel="stylesheet">
    <link href="{{"css/hybrid.css" | relURL}}{{ if $assetBusting }}?{{ now.Unix }}{{ end }}" rel="stylesheet">
    <link href="{{"css/featherlight.min.css" | relURL}}{{ if $assetBusting }}?{{ now.Unix }}{{ end }}" rel="stylesheet">
    <link href="{{"css/perfect-scrollbar.min.css" | relURL}}{{ if $assetBusting }}?{{ now.Unix }}{{ end }}" rel="stylesheet">
    <link href="{{"css/auto-complete.css" | relURL}}{{ if $assetBusting }}?{{ now.Unix }}{{ end }}" rel="stylesheet">
    <link href="{{"css/atom-one-dark-reasonable.css" | relURL}}{{ if $assetBusting }}?{{ now.Unix }}{{ end }}" rel="stylesheet">
    <link href="{{"css/theme.css" | relURL}}{{ if $assetBusting }}?{{ now.Unix }}{{ end }}" rel="stylesheet">
    <link href="{{"css/tabs.css" | relURL}}{{ if $assetBusting }}?{{ now.Unix }}{{ end }}" rel="stylesheet">
    <link href="{{"css/hugo-theme.css" | relURL}}{{ if $assetBusting }}?{{ now.Unix }}{{ end }}" rel="stylesheet">
    {{with .Site.Params.themeVariant}}
    <link href="{{(printf "css/theme-%s.css" .) | relURL}}{{ if $assetBusting }}?{{ now.Unix }}{{ end }}" rel="stylesheet">
    {{end}}
    {{ range .Site.Params.custom_css -}}
    <link href="{{(printf "%s" .) | relURL}}{{ if $assetBusting }}?{{ now.Unix }}{{ end }}" rel="stylesheet">
    {{- end }}

    <script src="{{"js/jquery-3.3.1.min.js"| relURL}}{{ if $assetBusting }}?{{ now.Unix }}{{ end }}"></script>

    <style>
      :root #header + #content > #left > #rlblock_left{
          display:none !important;
      }
      {{ if .Site.Params.disableInlineCopyToClipBoard }}
        :not(pre) > code + span.copy-to-clipboard {
            display: none;
        }
      {{ end }}
    </style>
    {{ partial "custom-header.html" . }}
  </head>
  <body class="" data-url="{{ .RelPermalink }}">
    {{ partial "menu.html" . }}
        <section id="body">
        <div id="overlay"></div>
        <div class="padding highlightable">
              {{if not .IsHome}}
              <div>
                <div id="top-bar">
                {{ if and (or .IsPage .IsSection) .Site.Params.editURL }}
                  {{ $File := .File }}
                  {{ $Site := .Site }}
                  {{with $File.Path }}
                  <div id="top-github-link">
                    <a class="github-link" title='{{T "Edit-this-page"}}' href="{{ $Site.Params.editURL }}{{ replace $File.Dir "\\" "/" }}{{ $File.LogicalName }}" target="blank">
                      <i class="fas fa-code-branch"></i>
                      <span id="top-github-link-text">{{T "Edit-this-page"}}</span>
                    </a>
                  </div>
                  {{ end }}
                {{ end }}
                {{$toc := (and (not .Params.disableToc) (not .Params.chapter))}}
                <div id="breadcrumbs" itemscope="" itemtype="http://data-vocabulary.org/Breadcrumb">
                    <span id="sidebar-toggle-span">
                        <a href="#" id="sidebar-toggle" data-sidebar-toggle="">
                          <i class="fas fa-bars"></i>
                        </a>
                    </span>
                  {{ if $toc }}
                  <span id="toc-menu"><i class="fas fa-list-alt"></i></span>
                  {{ end }}
                  <span class="links">
                 {{$showBreadcrumb := (and (not .Params.disableBreadcrumb) (not .Site.Params.disableBreadcrumb))}}
                 {{if $showBreadcrumb}}
                    {{ template "breadcrumb" dict "page" . "value" .Title }}
                 {{ else }}
                   {{ .Title }}
                 {{ end }}
                  </span>
                </div>
                {{ if $toc }}
                    {{ partial "toc.html" . }}
                {{ end }}
              </div>
            </div>
            {{ end }}
        <div id="head-tags">
        {{ partial "tags.html" . }}
        </div>
        {{ if .Params.chapter }}
          <div id="chapter">
        {{ end }}
        <div id="body-inner">
          {{if and (not .IsHome) (not .Params.chapter) }}
            <h1>
              {{ if or (eq .Kind "taxonomy") (eq .Kind "term") }}
                {{.Data.Singular}} ::
              {{ end }}
              {{.Title}}
            </h1>
          {{end}}

        {{define "breadcrumb"}}
          {{$parent := .page.Parent }}
          {{ if $parent }}
            {{ $value := (printf "<a href='%s'>%s</a> > %s" $parent.RelPermalink $parent.Title .value) }}
            {{ template "breadcrumb" dict "page" $parent "value" $value }}
          {{else}}
            {{.value|safeHTML}}
          {{end}}
        {{end}}
