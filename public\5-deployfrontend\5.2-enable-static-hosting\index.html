<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enable Static Hosting, Configure CORS, and Set S3 Bucket Permissions :: AWS System Manager</title>
    <link rel="stylesheet" href="/workshopFCJ/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <nav class="sidebar">
            <div class="logo">
    <a href="https://thuananwork.github.io/workshopFCJ/">
        <h2>AWS System Manager</h2>
    </a>
</div>

<nav class="menu">
    <ul>
        
    </ul>
    
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/1-introduce/">
            <strong>false. Introduction</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/">
            <strong>false. Preparation</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.1-installnodejs/">
            false. Installing NodeJS
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.2-installyarn/">
            false. Installing Yarn
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.3-installsamcli/">
            false. Installing SAM CLI
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.4-createiam/">
            false. Create Account and Configure IAM
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.5-create-google-oauth2/">
            false. Create Google OAuth2 Project
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.1-installnodejs/">
            <strong>false. Installing NodeJS</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.2-installyarn/">
            <strong>false. Installing Yarn</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.3-installsamcli/">
            <strong>false. Installing SAM CLI</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.4-createiam/">
            <strong>false. Create Account and Configure IAM</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.5-create-google-oauth2/">
            <strong>false. Create Google OAuth2 Project</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/3-deploybackend/">
            <strong>false. Deploying the Backend</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/3-deploybackend/3.1-deploy-backend/">
            false. Deploying the Backend with SAM CLI/CloudFormation
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/3-deploybackend/3.2-check-status-log-backend/">
            false. Checking Backend Status and Logs After Deployment
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/3-deploybackend/3.1-deploy-backend/">
            <strong>false. Deploying the Backend with SAM CLI/CloudFormation</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/3-deploybackend/3.2-check-status-log-backend/">
            <strong>false. Checking Backend Status and Logs After Deployment</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/4-testbackendapi/">
            <strong>false. Testing Backend APIs with Postman</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/4-testbackendapi/4.1-endpoint-api-gateway/">
            false. Retrieving the API Gateway Endpoint
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/4-testbackendapi/4.2-get-post-response/">
            false. Sending GET/POST Requests to Verify Backend Responses
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/4-testbackendapi/4.1-endpoint-api-gateway/">
            <strong>false. Retrieving the API Gateway Endpoint</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/4-testbackendapi/4.2-get-post-response/">
            <strong>false. Sending GET/POST Requests to Verify Backend Responses</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/">
            <strong>false. Deploy Frontend</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.1-frontend-s3/">
            false. Deploy Frontend to S3 Bucket
        </a>
    </li>
    
    <li class="page-item active">
        <a href="/workshopFCJ/5-deployfrontend/5.2-enable-static-hosting/">
            false. Enable Static Hosting, Configure CORS, and Set S3 Bucket Permissions
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.3-s3-bucket-permission/">
            false. Grant Public Permissions to S3 Bucket
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.4-clientid-clientserver/">
            false. Configure Google OAuth2 Client ID and Client Secret
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.5-connect-frontend-api-backend/">
            false. Connect the Frontend to the Backend API
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.1-frontend-s3/">
            <strong>false. Deploy Frontend to S3 Bucket</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.2-enable-static-hosting/">
            <strong>false. Enable Static Hosting, Configure CORS, and Set S3 Bucket Permissions</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.3-s3-bucket-permission/">
            <strong>false. Grant Public Permissions to S3 Bucket</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.4-clientid-clientserver/">
            <strong>false. Configure Google OAuth2 Client ID and Client Secret</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.5-connect-frontend-api-backend/">
            <strong>false. Connect the Frontend to the Backend API</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/6-ssl-s3-static/">
            <strong>false. Setup SSL S3 Static Website</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/7-demo/">
            <strong>false. Demo and Run the Project</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/8-cleanup/">
            <strong>false. Clean Up Resources</strong>
        </a>
    </li>
    
    
</ul>

    
</nav>



        </nav>
        <main class="content">
            
<article>
    <header>
        <h1>Enable Static Hosting, Configure CORS, and Set S3 Bucket Permissions</h1>
    </header>
    <div class="article-content">
        <h3 id="enable-static-hosting-configure-cors-and-grant-permissions-for-the-s3-bucket">Enable Static Hosting, Configure CORS, and Grant Permissions for the S3 Bucket</h3>
<p>After uploading your frontend source code to the S3 Bucket, you need to <strong>enable static website hosting</strong>, set up the index and error documents, configure CORS policy, and grant public read permissions so your website runs stably, is accessible from browsers, and can connect to the backend API.</p>
<h4 id="step-1-enable-static-hosting-and-configure-cors">Step 1: Enable Static Hosting and Configure CORS</h4>
<ol>
<li>
<p><strong>Enable static website hosting for the bucket</strong></p>
<ul>
<li>Go to your frontend bucket in the <a href="https://s3.console.aws.amazon.com/s3/">AWS S3 Console</a>.</li>
<li>Select the <strong>Properties</strong> tab.</li>
<li>Scroll down to <strong>Static website hosting</strong> and click <strong>Edit</strong>.
<img src="/images/static_website_hosting.png" alt="static_website_hosting"></li>
<li>Select <strong>Enable</strong>.</li>
<li>Enter <code>index.html</code> in the <strong>Index document</strong> field.</li>
<li>You may also enter <code>index.html</code> for the <strong>Error document</strong> field (recommended for SPA deployments).</li>
<li>Save the settings.
<img src="/images/enable_static_website_hosting.png" alt="static_website_hosting"></li>
<li>AWS will generate a <strong>Website endpoint</strong> like:
<pre tabindex="0"><code>http://fcjfashionshop.com.s3-website-ap-southeast-1.amazonaws.com
</code></pre></li>
<li>Copy this URL for the next step.
<img src="/images/bucket_website_hosting.png" alt="bucket_website_hosting"></li>
</ul>
</li>
<li>
<p><strong>Configure CORS for the bucket</strong></p>
<ul>
<li>Go to the <strong>Permissions</strong> tab → scroll down to <strong>CORS configuration</strong> → click <strong>Edit</strong>.
<img src="/images/cors.png" alt="cors"></li>
<li>Add the following sample configuration (JSON) to allow the frontend to access resources or upload images to S3 from other domains:
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-json" data-lang="json"><span style="display:flex;"><span>[
</span></span><span style="display:flex;"><span>  {
</span></span><span style="display:flex;"><span>    <span style="color:#f92672">&#34;AllowedOrigins&#34;</span>: [<span style="color:#e6db74">&#34;*&#34;</span>],
</span></span><span style="display:flex;"><span>    <span style="color:#f92672">&#34;AllowedMethods&#34;</span>: [<span style="color:#e6db74">&#34;GET&#34;</span>, <span style="color:#e6db74">&#34;HEAD&#34;</span>, <span style="color:#e6db74">&#34;PUT&#34;</span>, <span style="color:#e6db74">&#34;POST&#34;</span>, <span style="color:#e6db74">&#34;DELETE&#34;</span>],
</span></span><span style="display:flex;"><span>    <span style="color:#f92672">&#34;AllowedHeaders&#34;</span>: [<span style="color:#e6db74">&#34;*&#34;</span>]
</span></span><span style="display:flex;"><span>  }
</span></span><span style="display:flex;"><span>]
</span></span></code></pre></div></li>
<li>To restrict to a specific domain for better security:
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-json" data-lang="json"><span style="display:flex;"><span>[
</span></span><span style="display:flex;"><span>  {
</span></span><span style="display:flex;"><span>    <span style="color:#f92672">&#34;AllowedOrigins&#34;</span>: [<span style="color:#e6db74">&#34;https://your-domain.com&#34;</span>],
</span></span><span style="display:flex;"><span>    <span style="color:#f92672">&#34;AllowedMethods&#34;</span>: [<span style="color:#e6db74">&#34;GET&#34;</span>, <span style="color:#e6db74">&#34;HEAD&#34;</span>],
</span></span><span style="display:flex;"><span>    <span style="color:#f92672">&#34;AllowedHeaders&#34;</span>: [<span style="color:#e6db74">&#34;*&#34;</span>]
</span></span><span style="display:flex;"><span>  }
</span></span><span style="display:flex;"><span>]
</span></span></code></pre></div></li>
<li>Click <strong>Save</strong> to apply.</li>
</ul>
</li>
</ol>
<h4 id="step-2-grant-public-permissions-to-the-s3-bucket">Step 2: Grant Public Permissions to the S3 Bucket</h4>
<ol>
<li>
<p><strong>Set permissions for the Frontend Bucket</strong></p>
<ul>
<li>Go to the <strong>Permissions</strong> tab of the bucket, scroll down to <strong>Bucket policy</strong> → <strong>Edit</strong>.
<img src="/images/click_edit_bucket_policy.png" alt="click_edit_bucket_policy"></li>
<li>Add a policy to allow public read access, for example:
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-json" data-lang="json"><span style="display:flex;"><span>{
</span></span><span style="display:flex;"><span>  <span style="color:#f92672">&#34;Version&#34;</span>: <span style="color:#e6db74">&#34;2012-10-17&#34;</span>,
</span></span><span style="display:flex;"><span>  <span style="color:#f92672">&#34;Statement&#34;</span>: [
</span></span><span style="display:flex;"><span>    {
</span></span><span style="display:flex;"><span>      <span style="color:#f92672">&#34;Sid&#34;</span>: <span style="color:#e6db74">&#34;PublicReadGetObject&#34;</span>,
</span></span><span style="display:flex;"><span>      <span style="color:#f92672">&#34;Effect&#34;</span>: <span style="color:#e6db74">&#34;Allow&#34;</span>,
</span></span><span style="display:flex;"><span>      <span style="color:#f92672">&#34;Principal&#34;</span>: <span style="color:#e6db74">&#34;*&#34;</span>,
</span></span><span style="display:flex;"><span>      <span style="color:#f92672">&#34;Action&#34;</span>: <span style="color:#e6db74">&#34;s3:GetObject&#34;</span>,
</span></span><span style="display:flex;"><span>      <span style="color:#f92672">&#34;Resource&#34;</span>: <span style="color:#e6db74">&#34;arn:aws:s3:::fcjfashionshop.com/*&#34;</span>
</span></span><span style="display:flex;"><span>    }
</span></span><span style="display:flex;"><span>  ]
</span></span><span style="display:flex;"><span>}
</span></span></code></pre></div></li>
<li>Click <strong>Save</strong> to apply.
<img src="/images/edit_permission_bucket_avata.png" alt="edit_permission_bucket_avata"></li>
</ul>
</li>
<li>
<p><strong>Set permissions for the avatar upload bucket (if any)</strong></p>
<ul>
<li>Go to the <strong>Permissions</strong> tab of the bucket → <strong>Bucket policy</strong>.
<img src="/images/permission_upload_avata.png" alt="click_bucket_myfrontend"></li>
<li>Add a policy to allow public read access, for example:
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-json" data-lang="json"><span style="display:flex;"><span>{
</span></span><span style="display:flex;"><span>  <span style="color:#f92672">&#34;Version&#34;</span>: <span style="color:#e6db74">&#34;2012-10-17&#34;</span>,
</span></span><span style="display:flex;"><span>  <span style="color:#f92672">&#34;Statement&#34;</span>: [
</span></span><span style="display:flex;"><span>    {
</span></span><span style="display:flex;"><span>      <span style="color:#f92672">&#34;Sid&#34;</span>: <span style="color:#e6db74">&#34;PublicReadGetObject&#34;</span>,
</span></span><span style="display:flex;"><span>      <span style="color:#f92672">&#34;Effect&#34;</span>: <span style="color:#e6db74">&#34;Allow&#34;</span>,
</span></span><span style="display:flex;"><span>      <span style="color:#f92672">&#34;Principal&#34;</span>: <span style="color:#e6db74">&#34;*&#34;</span>,
</span></span><span style="display:flex;"><span>      <span style="color:#f92672">&#34;Action&#34;</span>: <span style="color:#e6db74">&#34;s3:GetObject&#34;</span>,
</span></span><span style="display:flex;"><span>      <span style="color:#f92672">&#34;Resource&#34;</span>: <span style="color:#e6db74">&#34;arn:aws:s3:::uploads-avatars-2025/*&#34;</span>
</span></span><span style="display:flex;"><span>    }
</span></span><span style="display:flex;"><span>  ]
</span></span><span style="display:flex;"><span>}
</span></span></code></pre></div></li>
<li>Click <strong>Save</strong> to apply.
<img src="/images/edit_permission_bucket_avata.png" alt="edit_permission_bucket_avata"></li>
</ul>
</li>
</ol>
<h4 id="step-3-verify-website-operation">Step 3: Verify Website Operation</h4>
<ul>
<li>Use the <strong>Website endpoint</strong> link to test your website.</li>
<li>If <code>index.html</code> loads successfully, your website is running on S3.
<img src="/images/front_s3_website.png" alt="front_website_s3"></li>
<li>If you encounter 403/404 errors:
<ul>
<li>Double-check your bucket policy permissions.</li>
<li>Make sure static hosting is enabled.</li>
<li>Verify that your index/error file names are correct.</li>
</ul>
</li>
</ul>
<div class="notice notice-warning">
    <div class="notice-icon">
<pre><code>        &lt;i class=&quot;fas fa-exclamation-triangle&quot;&gt;&lt;/i&gt;
    
&lt;/div&gt;
&lt;div class=&quot;notice-content&quot;&gt;
    &lt;strong&gt;Note:&lt;/strong&gt;&lt;br&gt;
</code></pre>
<p>You should only use <code>&quot;*&quot;</code> for development/testing. When deploying to production, always specify your real website domain in <code>AllowedOrigins</code> for better security.
</div></p>
</div>
<p><strong>Conclusion:</strong><br>
Properly enabling static hosting, configuring CORS, and setting public permissions will ensure your frontend website on S3 operates reliably, can call the backend API, and provides optimal load speed and user experience.</p>

        
    </div>
</article>

        </main>
    </div>
</body>
</html>
