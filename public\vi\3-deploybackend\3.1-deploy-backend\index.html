<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Triển khai backend bằng SAM CLI/CloudFormation :: AWS System Manager</title>
    <link rel="stylesheet" href="/workshopFCJ/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <nav class="sidebar">
            <div class="logo">
    <a href="https://thuananwork.github.io/workshopFCJ/">
        <h2>AWS System Manager</h2>
    </a>
</div>

<nav class="menu">
    <ul>
        
    </ul>
    
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/1-introduce/">
            <strong>false. Giới thiệu</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/">
            <strong>false. Chuẩn bị môi trường</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/2-prerequiste/2.1-installnodejs/">
            false. Cài đặt NodeJS
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/2-prerequiste/2.2-installyarn/">
            false. Cài đặt Yarn
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/2-prerequiste/2.3-installsamcli/">
            false. Cài đặt SAM CLI
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/2-prerequiste/2.4-createiam/">
            false. Tạo tài khoản và cấu hình IAM
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/2-prerequiste/2.5-create-google-oauth2/">
            false. Tạo Google OAuth2 Project
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/2.1-installnodejs/">
            <strong>false. Cài đặt NodeJS</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/2.2-installyarn/">
            <strong>false. Cài đặt Yarn</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/2.3-installsamcli/">
            <strong>false. Cài đặt SAM CLI</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/2.4-createiam/">
            <strong>false. Tạo tài khoản và cấu hình IAM</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/2-prerequiste/2.5-create-google-oauth2/">
            <strong>false. Tạo Google OAuth2 Project</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/3-deploybackend/">
            <strong>false. Triển khai backend</strong>
        </a>
    </li>
    
    <li class="page-item active">
        <a href="/workshopFCJ/vi/3-deploybackend/3.1-deploy-backend/">
            false. Triển khai backend bằng SAM CLI/CloudFormation
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/3-deploybackend/3.2-check-status-log-backend/">
            false. Kiểm tra trạng thái và log backend sau khi triển khai
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/3-deploybackend/3.1-deploy-backend/">
            <strong>false. Triển khai backend bằng SAM CLI/CloudFormation</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/3-deploybackend/3.2-check-status-log-backend/">
            <strong>false. Kiểm tra trạng thái và log backend sau khi triển khai</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/4-testbackendapi/">
            <strong>false. Kiểm thử API backend với Postman</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/4-testbackendapi/4.1-endpoint-api-gateway/">
            false. Lấy endpoint API Gateway
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/4-testbackendapi/4.2-get-post-response/">
            false. Gửi request GET/POST để kiểm tra response backend
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/4-testbackendapi/4.1-endpoint-api-gateway/">
            <strong>false. Lấy endpoint API Gateway</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/4-testbackendapi/4.2-get-post-response/">
            <strong>false. Gửi request GET/POST để kiểm tra response backend</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/">
            <strong>false. Triển khai frontend</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.1-frontend-s3/">
            false. Triển khai frontend lên S3 Bucket
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.2-enable-static-hosting/">
            false. Bật static hosting, cấu hình CORS
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.3-s3-bucket-permission/">
            false. Cấp quyền public cho S3 Bucket
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.4-clientid-clientserver/">
            false. Cấu hình Google OAuth2 Client ID và Client Secret
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.5-connect-frontend-api-backend/">
            false. Kết nối frontend với API backend
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.1-frontend-s3/">
            <strong>false. Triển khai frontend lên S3 Bucket</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.2-enable-static-hosting/">
            <strong>false. Bật static hosting, cấu hình CORS</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.3-s3-bucket-permission/">
            <strong>false. Cấp quyền public cho S3 Bucket</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.4-clientid-clientserver/">
            <strong>false. Cấu hình Google OAuth2 Client ID và Client Secret</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/5-deployfrontend/5.5-connect-frontend-api-backend/">
            <strong>false. Kết nối frontend với API backend</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/6-ssl-s3-static/">
            <strong>false. Thiết lập trang web SSL S3 Static</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/7-demo/">
            <strong>false. Demo và chạy dự án</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/vi/8-cleanup/">
            <strong>false. Dọn dẹp tài nguyên</strong>
        </a>
    </li>
    
    
</ul>

    
</nav>



        </nav>
        <main class="content">
            
<article>
    <header>
        <h1>Triển khai backend bằng SAM CLI/CloudFormation</h1>
    </header>
    <div class="article-content">
        <h4 id="triển-khai-backend-bằng-sam-clicloudformation">Triển khai Backend bằng SAM CLI/CloudFormation</h4>
<p>Trong phần này, bạn sẽ triển khai backend cho website thương mại điện tử động của mình bằng <strong>SAM CLI</strong> và <strong>CloudFormation</strong>. Chúng ta sẽ sử dụng file <strong><code>template.yaml</code></strong> có sẵn trong dự án để tự động tạo các tài nguyên như <strong>API Gateway</strong>, <strong>Lambda</strong>, và <strong>DynamoDB</strong>.</p>
<h4 id="bước-1-cấu-hình-aws-cli">Bước 1: Cấu hình AWS CLI</h4>
<p>Trước khi bắt đầu sử dụng <strong>SAM CLI</strong> để triển khai, bạn cần phải cấu hình <strong>AWS CLI</strong> với các thông tin đăng nhập của tài khoản AWS. Điều này giúp <strong>SAM CLI</strong> có thể sử dụng các quyền truy cập đã được cấp cho IAM User của bạn.</p>
<ol>
<li>
<p><strong>Chạy lệnh <code>aws configure</code></strong>:</p>
<ul>
<li>
<p>Mở terminal và nhập lệnh sau để cấu hình AWS CLI:</p>
<pre><code> aws configure
</code></pre>
</li>
</ul>
</li>
<li>
<p><strong>Nhập thông tin cấu hình</strong>:</p>
<ul>
<li>
<p><strong>AWS Access Key ID</strong>: Nhập <strong>Access Key ID</strong> mà bạn đã tạo khi tạo IAM User.</p>
</li>
<li>
<p><strong>AWS Secret Access Key</strong>: Nhập <strong>Secret Access Key</strong> tương ứng với Access Key ID.</p>
</li>
<li>
<p><strong>Default region name</strong>: Nhập <code>ap-southeast-1</code> (cho Singapore).</p>
</li>
<li>
<p><strong>Default output format</strong>: Bạn có thể nhập <code>json</code> , hoặc để mặc định <code>None</code>.
<img src="/images/aws_configure.png" alt="Error image"></p>
</li>
<li>
<p>Sau khi hoàn tất cấu hình, AWS CLI sẽ lưu thông tin cấu hình trong file <code>~/.aws/credentials</code> (Linux/macOS) hoặc <code>C:\Users\<USER>\.aws\credentials</code> (Windows).</p>
</li>
</ul>
</li>
</ol>
<h4 id="bước-2-triển-khai-backend-bằng-sam-cli">Bước 2: Triển khai Backend bằng SAM CLI</h4>
<ol>
<li>
<p><strong>Kiểm tra file <code>template.yaml</code></strong>:</p>
<ul>
<li>Nếu bạn chưa có file <strong><code>template.yaml</code></strong>, hãy kiểm tra trong thư mục dự án của bạn. File này sẽ định nghĩa các tài nguyên như <strong>API Gateway</strong>, <strong>Lambda</strong>, và <strong>DynamoDB</strong>.</li>
<li>Nếu file này có sẵn trong dự án, đảm bảo rằng nó đã được cấu hình chính xác cho các tài nguyên bạn cần.</li>
</ul>
</li>
<li>
<p><strong>Build dự án</strong>:</p>
<ul>
<li>
<p>Sau khi đảm bảo file <strong><code>template.yaml</code></strong> đã có trong dự án</p>
</li>
<li>
<p>Truy cập vào thư mục backend của dự án sử dụng <strong>SAM CLI</strong> để build dự án:</p>
<pre><code> sam build
</code></pre>
</li>
<li>
<p>Nếu bạn thấy dòng chữ <strong>Build Succeeded</strong> bạn đã cài đặt thành công SAM CLI
<img src="/images/build_success_samcli.png" alt="Error image"></p>
</li>
</ul>
</li>
<li>
<p><strong>Validate Template</strong>:</p>
<ul>
<li>
<p>Kiểm tra lại cấu hình của file <code>template.yaml</code> để chắc chắn rằng không có lỗi cú pháp hoặc cấu hình:</p>
</li>
<li>
<p>Gõ lệnh <strong>sam validate</strong> để kiểm tra file template.yaml:</p>
<pre><code> sam validate
</code></pre>
</li>
<li>
<p>Nếu bạn thấy thông báo như bên dưới, thì file <strong>template.yaml</strong> của bạn hợp lệ.
<img src="/images/sam_validate.png" alt="Error image"></p>
</li>
</ul>
</li>
<li>
<p><strong>Triển khai tài nguyên lên AWS</strong>:</p>
<ul>
<li>
<p>Sau khi hoàn tất build và validate, bạn có thể triển khai tài nguyên lên AWS bằng lệnh:</p>
<pre><code> sam deploy --guided
</code></pre>
</li>
<li>
<p>Khi sử dụng <strong>SAM CLI</strong> để triển khai, bạn sẽ được yêu cầu nhập các thông tin cấu hình.</p>
<ul>
<li><strong>Stack Name</strong>: <code>sam-app</code></li>
<li><strong>AWS Region</strong>: <code>ap-southeast-1</code></li>
<li><strong>Confirm changes before deploy</strong>: <code>y</code></li>
<li><strong>Allow SAM CLI IAM role creation</strong>: <code>y</code></li>
<li><strong>Disable rollback</strong>: <code>n</code> (Không tắt rollback)</li>
<li><strong>ExpressApiFunction has no authentication. Is this okay?</strong>: <code>y</code></li>
<li><strong>Save arguments to configuration file</strong>: <code>y</code></li>
<li><strong>SAM configuration file</strong>: Gõ phím <strong>Enter</strong></li>
<li><strong>SAM configuration environment</strong>: Gõ phím <strong>Enter</strong></li>
</ul>
</li>
<li>
<p>Khi sử dụng <strong>SAM CLI</strong> để triển khai, bạn sẽ được yêu cầu nhập các thông tin cấu hình.</p>
<p><img src="/images/deploy_this_changeset.png" alt="Error image"></p>
<ul>
<li><strong>Deploy this changeset</strong>: <code>y</code></li>
<li>Bạn đợi khoảng 5 phút để cấu hình serverless (API Gateway, Lambda, DynamoDB, v.v.) từ máy tính của bạn lên AWS.</li>
</ul>
<p><img src="/images/create_sam_app_success.png" alt="Error image"></p>
</li>
</ul>
</li>
<li>
<p><strong>Xác nhận triển khai</strong>:</p>
<ul>
<li>Sau khi triển khai thành công, SAM CLI sẽ cung cấp thông tin về các tài nguyên đã được tạo. Bạn có thể kiểm tra <strong>API Gateway</strong>, <strong>Lambda function</strong>, <strong>S3</strong>, <strong>CLOUD FOUMATION</strong> và <strong>DynamoDB</strong> trong AWS Management Console để xác nhận các tài nguyên đã được tạo đúng.</li>
</ul>
</li>
</ol>
<div class="notice notice-tip">
    <div class="notice-icon">
<pre><code>        &lt;i class=&quot;fas fa-lightbulb&quot;&gt;&lt;/i&gt;
    
&lt;/div&gt;
&lt;div class=&quot;notice-content&quot;&gt;
    Backend của bạn đã được triển khai thành công! Các tài nguyên như API Gateway, Lambda function và DynamoDB đã được tự động tạo. Bạn có thể kiểm tra trên AWS Console.
&lt;/div&gt;
</code></pre>
</div>
<pre><code> - Truy cập vào [API Gateway](https://ap-southeast-1.console.aws.amazon.com/apigateway/main/)  bạn sẽ thấy API Gateway đã được tạo

  ![Error image](/images/create_API_gateway.png)

 - Truy cập vào [Lambda function](https://ap-southeast-1.console.aws.amazon.com/lambda/home/<USER>

  ![Error image](/images/create_auto_lambda.png)

 - Truy cập vào [Amazon S3](https://ap-southeast-1.console.aws.amazon.com/s3/home)  bạn sẽ thấy Amazon S3 đã được tạo

  ![Error image](/images/access_s3_after_setup_samcli.png)

 - Truy cập vào [Cloud Foumation](https://ap-southeast-1.console.aws.amazon.com/cloudformation/home)  bạn sẽ thấy Cloud Foumation đã được tạo

  ![Error image](/images/create_auto_cloud_foumation.png)

 - Truy cập vào [DynamoDB](hhttps://ap-southeast-1.console.aws.amazon.com/dynamodbv2/home?region=ap-southeast-1#tables)  bạn sẽ thấy DynamoDB đã được tạo

  ![Error image](/images/create_auto_dynamoDB.png)
</code></pre>

        
    </div>
</article>

        </main>
    </div>
</body>
</html>
