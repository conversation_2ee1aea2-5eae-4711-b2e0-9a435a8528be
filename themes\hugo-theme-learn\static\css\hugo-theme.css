/* Insert here special css for hugo theme, on top of any other imported css */


/* Table of contents */

.progress ul {
  list-style: none;
  margin: 0;
  padding: 0 15px;
}

#TableOfContents {
    font-size: 13px !important;
    max-height: 85vh;
    overflow: auto;
    padding: 15px 5px !important;
}

#TableOfContents > ul > li > a {
  font-weight: bold;
}

body {
    font-size: 16px !important;
    color: #323232 !important;
}

#body a.highlight, #body a.highlight:hover, #body a.highlight:focus {
    text-decoration: none;
    outline: none;
    outline: 0;
}
#body a.highlight {
    line-height: 1.1;
    display: inline-block;
}
#body a.highlight:after {
    display: block;
    content: "";
    height: 1px;
    width: 0%;
    background-color: #0082a7; /*#CE3B2F*/
    -webkit-transition: width 0.5s ease;
    -moz-transition: width 0.5s ease;
    -ms-transition: width 0.5s ease;
    transition: width 0.5s ease;
}
#body a.highlight:hover:after, #body a.highlight:focus:after {
    width: 100%;
}
.progress {
    position:absolute;
    background-color: rgba(246, 246, 246, 0.97);
    width: auto;
    border: thin solid #ECECEC;
    display:none;
    z-index:200;
}

#toc-menu {
  border-right: thin solid #DAD8D8 !important;
  padding-right: 1rem !important;
  margin-right: 0.5rem !important;
}

#sidebar-toggle-span {
  border-right: thin solid #DAD8D8 !important;
  padding-right: 0.5rem !important;
  margin-right: 1rem !important;
}

.btn {
  display: inline-block !important;
  padding: 6px 12px !important;
  margin-bottom: 0 !important;
  font-size: 14px !important;
  font-weight: normal !important;
  line-height: 1.42857143 !important;
  text-align: center !important;
  white-space: nowrap !important;
  vertical-align: middle !important;
  -ms-touch-action: manipulation !important;
      touch-action: manipulation !important;
  cursor: pointer !important;
  -webkit-user-select: none !important;
     -moz-user-select: none !important;
      -ms-user-select: none !important;
          user-select: none !important;
  background-image: none !important;
  border: 1px solid transparent !important;
  border-radius: 4px !important;
  -webkit-transition: all 0.15s !important;
     -moz-transition: all 0.15s !important;
          transition: all 0.15s !important;
}
.btn:focus {
  /*outline: thin dotted;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;*/
  outline: none !important;
}
.btn:hover,
.btn:focus {
  color: #2b2b2b !important;
  text-decoration: none !important;
}

.btn-default {
  color: #333 !important;
  background-color: #fff !important;
  border-color: #ccc !important;
}
.btn-default:hover,
.btn-default:focus,
.btn-default:active {
  color: #fff !important;
  background-color: #9e9e9e !important;
  border-color: #9e9e9e !important;
}
.btn-default:active {
  background-image: none !important;
}

/* anchors */
.anchor {
  color: #00bdf3;
  font-size: 0.5em;
  cursor:pointer;
  visibility:hidden;
  margin-left: 0.5em;
    position: absolute;
    margin-top:0.1em;
}

h2:hover .anchor, h3:hover .anchor, h4:hover .anchor, h5:hover .anchor, h6:hover .anchor {
  visibility:visible;
}

/* Redfines headers style */

h2, h3, h4, h5, h6 {
  font-weight: 400;
  line-height: 1.1;
}

h1 a, h2 a, h3 a, h4 a, h5 a, h6 a {
  font-weight: inherit;
}

h2 {
  font-size: 2.5rem;
  line-height: 110% !important;
  margin: 2.5rem 0 1.5rem 0;
}

h3 {
  font-size: 2rem;
  line-height: 110% !important;
  margin: 2rem 0 1rem 0;
}

h4 {
  font-size: 1.5rem;
  line-height: 110% !important;
  margin: 1.5rem 0 0.75rem 0;
}

h5 {
  font-size: 1rem;
  line-height: 110% !important;
  margin: 1rem 0 0.2rem 0;
}

h6 {
  font-size: 0.5rem;
  line-height: 110% !important;
  margin: 0.5rem 0 0.2rem 0;
}

p {
    margin: 1rem 0;
}

figcaption h4 {
    font-weight: 300 !important;
    opacity: .85;
    font-size: 1em;
    text-align: center;
    margin-top: -1.5em;
}

.select-style {
    border: 0;
    width: 150px;
    border-radius: 0px;
    overflow: hidden;
    display: inline-flex;
}

.select-style svg {
    fill: #ccc;
    width: 14px;
    height: 14px;
    pointer-events: none;
    margin: auto;
}

.select-style svg:hover {
  fill: #e6e6e6;
}

.select-style select {
    padding: 0;
    width: 130%;
    border: none;
    box-shadow: none;
    background: transparent;
    background-image: none;
    -webkit-appearance: none;
    margin: auto;
    margin-left: 0px;
    margin-right: -20px;
}

.select-style select:focus {
    outline: none;
}

.select-style :hover {
    cursor:  pointer;
}

@media only all and (max-width: 47.938em) {
  #breadcrumbs .links, #top-github-link-text {
      display: none;
  }
}

.is-sticky #top-bar {
  box-shadow: -1px 2px 5px 1px rgba(0, 0, 0, 0.1); 
}