---
title: "Dynamic E-Commerce Website"
date: 2023-10-25
weight: 10
chapter: false
---

# Building a Dynamic E-Commerce Website with AWS Serverless

### Overview

In this workshop, you will deploy a **dynamic e-commerce website** using the Hugo framework on the AWS Cloud platform. You will learn how to prepare your environment, configure your AWS account, build the website with <PERSON>, and deploy the entire system using essential AWS services such as API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch, and leverage SAM CLI to automate the deployment process.

Additionally, you will be guided through **setting up a static website on S3 with full SSL (HTTPS) support** to enhance security and professionalism, ensuring your website serves customers globally with confidence.
![<PERSON><PERSON><PERSON> trúc tổng thể](/images/fcjfashionshop_drawio.png)

### Workshop Objectives

- Gain proficiency in essential AWS tools required for a dynamic website project.
- Learn to prepare, install, and configure the development environment for a Hugo project.
- Build, package, and deploy a dynamic website with <PERSON>, integrating modern AWS serverless services.
- Design and deploy APIs with API Gateway, implement business logic using Lambda, store data with DynamoDB, and manage your dynamic website on S3.
- Use CloudFormation to automate AWS resource creation and configuration; monitor and observe system activities with CloudWatch.
- **Set up a static website on S3 integrated with SSL (HTTPS) using AWS Certificate Manager and CloudFront.**
- Configure custom domains and DNS resolution with Route 53 for public internet access to your dynamic website.
- Apply DevOps workflows to automate deployment and operations of your website efficiently on AWS.

### What You Will Learn from This Workshop

After completing this workshop, you will:

- Clearly understand the architecture and deployment workflow of a dynamic e-commerce website on AWS.
- Learn to use AWS services such as API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch, and SAM CLI in a real-world project.
- Know how to build, package, and deploy a dynamic website with Hugo, connecting the frontend with the backend via API Gateway and Lambda.
- **Be able to set up a static website on S3, use CloudFront and AWS Certificate Manager to enable HTTPS for your website.**
- Practice managing dynamic data with DynamoDB and automate infrastructure with CloudFormation.
- Configure custom domains with Route 53, monitor your system, and analyze application logs via CloudWatch.
- Be ready to apply these skills to real-world projects, dynamic websites, serverless architectures, or DevOps workflows on AWS.

### Contents
 1. [Introduction](1-introduce/)
 2. [Preparation Steps](2-Prerequiste/)
 3. [Deploy Backend](3-deployBackend/)
 4. [Test Backend API with Postman](4-testBackendApi/)
 5. [Deploy Frontend](5-deployFrontend/)
 6. [Setup SSL S3 Static Website](6-ssl-s3-static/)
 7. [Demo and Project Run](7-demo/)
 8. [Clean Up Resources](8-cleanup/)
