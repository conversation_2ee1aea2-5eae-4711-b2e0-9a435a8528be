<nav id="sidebar" class="{{if $.Site.Params.showVisitedLinks }}showVisitedLinks{{end}}">

  {{ $currentNode := . }}
  {{ $showvisitedlinks := .Site.Params.showVisitedLinks }}
  <div id="header-wrapper">
    <div id="header">
      {{ partial "logo.html" . }}
    </div>
    {{if not .Site.Params.disableSearch}}
    {{ partial "search.html" . }}
    {{end}}
  </div>

  <div class="highlightable">
    <ul class="topics">

      {{if eq .Site.Params.ordersectionsby "title"}}
      {{range .Site.Home.Sections.ByTitle}}
      {{ template "section-tree-nav" dict "sect" . "currentnode" $currentNode "showvisitedlinks" $showvisitedlinks}}
      {{end}}
      {{else}}
      {{range .Site.Home.Sections.ByWeight}}
      {{ template "section-tree-nav" dict "sect" . "currentnode" $currentNode "showvisitedlinks" $showvisitedlinks}}
      {{end}}
      {{end}}
    </ul>

    {{ $disableShortcutsTitle := .Site.Params.DisableShortcutsTitle}}
    {{with .Site.Menus.shortcuts}}
    <section id="shortcuts">
      <h3>{{ if not $disableShortcutsTitle}}{{ T "Shortcuts-Title"}}{{ end }}</h3>
      <ul>
        {{ range sort . "Weight"}}
        <li>
          {{.Pre}}<a class="padding" href="{{.URL | absLangURL }}">{{safeHTML .Name}}</a>{{.Post}}
        </li>
        {{end}}
      </ul>
    </section>
    {{end}}

    {{ if or hugo.IsMultilingual $showvisitedlinks }}
    <section id="prefooter">
      <hr />
      <ul>
        {{ if and hugo.IsMultilingual (not .Site.Params.DisableLanguageSwitchingButton)}}
        <li>
          <a class="padding">
            <i class="fas fa-language fa-fw"></i>
            <div class="select-style">
              <select id="select-language" onchange="location = this.value;">
                {{ $siteLanguages := .Site.Languages}}
                {{ $pageLang := .Page.Lang}}
                {{ range .Page.AllTranslations }}
                {{ $translation := .}}
                {{ range $siteLanguages }}
                {{ if eq $translation.Lang .Lang }}
                {{ $selected := false }}
                {{ if eq $pageLang .Lang}}
                <option id="{{ $translation.Language }}" value="{{ $translation.Permalink }}" selected>{{ .LanguageName
                  }}</option>
                {{ else }}
                <option id="{{ $translation.Language }}" value="{{ $translation.Permalink }}">{{ .LanguageName }}
                </option>
                {{ end }}
                {{ end }}
                {{ end }}
                {{ end }}
              </select>
              <svg version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="255px" height="255px"
                viewBox="0 0 255 255" style="enable-background:new 0 0 255 255;" xml:space="preserve">
                <g>
                  <g id="arrow-drop-down">
                    <polygon points="0,63.75 127.5,191.25 255,63.75 		" />
                  </g>
                </g>
              </svg>
            </div>
          </a>
        </li>
        {{end}}

        {{ if $showvisitedlinks}}
        <li><a class="padding" href="#" data-clear-history-toggle=""><i class="fas fa-history fa-fw"></i> {{T
            "Clear-History"}}</a></li>
        {{ end }}
      </ul>
    </section>
    {{ end }}
    <section id="footer">
      {{ partial "menu-footer.html" . }}
    </section>
  </div>
</nav>

<!-- templates -->
{{ define "section-tree-nav" }}
{{ $showvisitedlinks := .showvisitedlinks }}
{{ $currentNode := .currentnode }}
{{ $currentFileUniqueID := "" }}
{{ with $currentNode.File }}{{ $currentFileUniqueID = .UniqueID }}{{ end }}
{{with .sect}}
{{if and .IsSection (or (not .Params.hidden) $.showhidden)}}
{{safeHTML .Params.head}}
<li data-nav-id="{{.RelPermalink}}" title="{{.Title}}" class="dd-item 
        {{if .IsAncestor $currentNode }}parent{{end}}
        {{if eq .File.UniqueID $currentFileUniqueID}}active{{end}}
        {{if .Params.alwaysopen}}parent{{end}}
        ">
  <a href="{{.RelPermalink}}">
    {{safeHTML .Params.Pre}}{{or .Params.menuTitle .LinkTitle .Title}}{{safeHTML .Params.Post}}
    {{ if $showvisitedlinks}}
    <i class="fas fa-check read-icon"></i>
    {{ end }}
  </a>
  {{ $numberOfPages := (add (len .Pages) (len .Sections)) }}
  {{ if ne $numberOfPages 0 }}
  <ul>
    {{ $currentNode.Scratch.Set "pages" .Pages }}
    {{ if .Sections}}
    {{ $currentNode.Scratch.Set "pages" (.Pages | union .Sections) }}
    {{end}}
    {{ $pages := ($currentNode.Scratch.Get "pages") }}

    {{if eq .Site.Params.ordersectionsby "title"}}
    {{ range $pages.ByTitle }}
    {{ if and .Params.hidden (not $.showhidden) }}
    {{else}}
    {{ template "section-tree-nav" dict "sect" . "currentnode" $currentNode "showvisitedlinks" $showvisitedlinks }}
    {{end}}
    {{ end }}
    {{else}}
    {{ range $pages.ByWeight }}
    {{ if and .Params.hidden (not $.showhidden) }}
    {{else}}
    {{ template "section-tree-nav" dict "sect" . "currentnode" $currentNode "showvisitedlinks" $showvisitedlinks }}
    {{end}}
    {{ end }}
    {{end}}
  </ul>
  {{ end }}
</li>
{{else}}
{{ if not .Params.Hidden }}
<li data-nav-id="{{.RelPermalink}}" title="{{.Title}}"
  class="dd-item {{if eq .File.UniqueID $currentFileUniqueID}}active{{end}}">
  <a href="{{ .RelPermalink}}">
    {{safeHTML .Params.Pre}}{{or .Params.menuTitle .LinkTitle .Title}}{{safeHTML .Params.Post}}
    {{ if $showvisitedlinks}}<i class="fas fa-check read-icon"></i>{{end}}
  </a>
</li>
{{ end }}
{{end}}
{{ end }}
{{ end }}