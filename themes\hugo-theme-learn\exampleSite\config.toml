baseURL = "/"
languageCode = "en-US"
defaultContentLanguage = "en"

title = "Hugo Learn Documentation"
theme = "hugo-theme-learn"
themesdir = "../.."
metaDataFormat = "yaml"
defaultContentLanguageInSubdir= true

[params]
  editURL = "https://github.com/matcornic/hugo-theme-learn/edit/master/exampleSite/content/"
  description = "Documentation for Hugo Learn Theme"
  author = "Mathieu Cornic"
  showVisitedLinks = true
  disableBreadcrumb = false
  disableNextPrev = false

[outputs]
home = [ "HTML", "RSS", "JSON"]

[Languages]
[Languages.en]
title = "Documentation for Hugo Learn Theme"
weight = 1
languageName = "English"

[[Languages.en.menu.shortcuts]]
name = "<i class='fab fa-fw fa-github'></i> GitHub repo"
identifier = "ds"
url = "https://github.com/matcornic/hugo-theme-learn"
weight = 10

[[Languages.en.menu.shortcuts]]
name = "<i class='fas fa-fw fa-camera'></i> Showcases"
url = "showcase"
weight = 11

[[Languages.en.menu.shortcuts]]
name = "<i class='fas fa-fw fa-bookmark'></i> Hugo Documentation"
identifier = "hugodoc"
url = "https://gohugo.io/"
weight = 20

[[Languages.en.menu.shortcuts]]
name = "<i class='fas fa-fw fa-bullhorn'></i> Credits"
url = "/credits"
weight = 30

[Languages.fr]
title = "Documentation du thème Hugo Learn"
weight = 2
languageName = "Français"

[[Languages.fr.menu.shortcuts]]
name = "<i class='fab fa-fw fa-github'></i> Repo GitHub"
identifier = "ds"
url = "https://github.com/matcornic/hugo-theme-learn"
weight = 10

[[Languages.fr.menu.shortcuts]]
name = "<i class='fas fa-fw fa-camera'></i> Vitrine"
url = "/showcase"
weight = 11

[[Languages.fr.menu.shortcuts]]
name = "<i class='fas fa-fw fa-bookmark'></i> Documentation Hugo"
identifier = "hugodoc"
url = "https://gohugo.io/"
weight = 20

[[Languages.fr.menu.shortcuts]]
name = "<i class='fas fa-fw fa-bullhorn'></i> Crédits"
url = "/credits"
weight = 30

[Languages.zh]
title = "Hugo 主题的 Learn 文档"
weight = 3
languageName = "简体中文"

[[Languages.zh.menu.shortcuts]]
name = "<i class='fab fa-fw fa-github'></i> GitHub 仓库"
identifier = "ds"
url = "https://github.com/matcornic/hugo-theme-learn"
weight = 10

[[Languages.zh.menu.shortcuts]]
name = "<i class='fas fa-fw fa-camera'></i> 展示区"
url = "/showcase"
weight = 11

[[Languages.zh.menu.shortcuts]]
name = "<i class='fas fa-fw fa-bookmark'></i> Hugo 文档"
identifier = "hugodoc"
url = "https://gohugo.io/"
weight = 20

[[Languages.zh.menu.shortcuts]]
name = "<i class='fas fa-fw fa-bullhorn'></i> 鸣谢"
url = "/credits"
weight = 30
