<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Preparation :: AWS System Manager</title>
    <link rel="stylesheet" href="/workshopFCJ/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <nav class="sidebar">
            <div class="logo">
    <a href="https://thuananwork.github.io/workshopFCJ/">
        <h2>AWS System Manager</h2>
    </a>
</div>

<nav class="menu">
    <ul>
        
    </ul>
    
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/1-introduce/">
            <strong>false. Introduction</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/">
            <strong>false. Preparation</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.1-installnodejs/">
            false. Installing NodeJS
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.2-installyarn/">
            false. Installing Yarn
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.3-installsamcli/">
            false. Installing SAM CLI
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.4-createiam/">
            false. Create Account and Configure IAM
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.5-create-google-oauth2/">
            false. Create Google OAuth2 Project
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.1-installnodejs/">
            <strong>false. Installing NodeJS</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.2-installyarn/">
            <strong>false. Installing Yarn</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.3-installsamcli/">
            <strong>false. Installing SAM CLI</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.4-createiam/">
            <strong>false. Create Account and Configure IAM</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.5-create-google-oauth2/">
            <strong>false. Create Google OAuth2 Project</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/3-deploybackend/">
            <strong>false. Deploying the Backend</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/3-deploybackend/3.1-deploy-backend/">
            false. Deploying the Backend with SAM CLI/CloudFormation
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/3-deploybackend/3.2-check-status-log-backend/">
            false. Checking Backend Status and Logs After Deployment
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/3-deploybackend/3.1-deploy-backend/">
            <strong>false. Deploying the Backend with SAM CLI/CloudFormation</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/3-deploybackend/3.2-check-status-log-backend/">
            <strong>false. Checking Backend Status and Logs After Deployment</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/4-testbackendapi/">
            <strong>false. Testing Backend APIs with Postman</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/4-testbackendapi/4.1-endpoint-api-gateway/">
            false. Retrieving the API Gateway Endpoint
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/4-testbackendapi/4.2-get-post-response/">
            false. Sending GET/POST Requests to Verify Backend Responses
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/4-testbackendapi/4.1-endpoint-api-gateway/">
            <strong>false. Retrieving the API Gateway Endpoint</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/4-testbackendapi/4.2-get-post-response/">
            <strong>false. Sending GET/POST Requests to Verify Backend Responses</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/">
            <strong>false. Deploy Frontend</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.1-frontend-s3/">
            false. Deploy Frontend to S3 Bucket
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.2-enable-static-hosting/">
            false. Enable Static Hosting, Configure CORS, and Set S3 Bucket Permissions
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.3-s3-bucket-permission/">
            false. Grant Public Permissions to S3 Bucket
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.4-clientid-clientserver/">
            false. Configure Google OAuth2 Client ID and Client Secret
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.5-connect-frontend-api-backend/">
            false. Connect the Frontend to the Backend API
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.1-frontend-s3/">
            <strong>false. Deploy Frontend to S3 Bucket</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.2-enable-static-hosting/">
            <strong>false. Enable Static Hosting, Configure CORS, and Set S3 Bucket Permissions</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.3-s3-bucket-permission/">
            <strong>false. Grant Public Permissions to S3 Bucket</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.4-clientid-clientserver/">
            <strong>false. Configure Google OAuth2 Client ID and Client Secret</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.5-connect-frontend-api-backend/">
            <strong>false. Connect the Frontend to the Backend API</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/6-ssl-s3-static/">
            <strong>false. Setup SSL S3 Static Website</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/7-demo/">
            <strong>false. Demo and Run the Project</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/8-cleanup/">
            <strong>false. Clean Up Resources</strong>
        </a>
    </li>
    
    
</ul>

    
</nav>



        </nav>
        <main class="content">
            
<article>
    <header>
        <h1>Preparation</h1>
    </header>
    <div class="article-content">
        <p>Before you begin deploying your dynamic e-commerce website using AWS services like <strong>API Gateway</strong>, <strong>S3</strong>, <strong>Lambda</strong>, <strong>CloudFormation</strong>, <strong>DynamoDB</strong>, <strong>Route 53</strong>, <strong>CloudWatch</strong>, and <strong>SAM CLI</strong>, you need to complete a few basic environment preparation steps. These tools and services will help you quickly, securely, and efficiently deploy your application.</p>
<p>In this section, you will perform the necessary setup steps for both the frontend and backend environments of the project:</p>
<ul>
<li><strong>Install NodeJS and Yarn</strong>: To support frontend build and management.</li>
<li><strong>Install SAM CLI</strong>: To deploy the serverless backend on AWS.</li>
<li><strong>Create AWS account and configure IAM</strong>: To set up access and security for the AWS environment.</li>
</ul>
<p>These preparation steps will ensure that you have the proper development environment in place and working correctly before proceeding with deploying AWS services for your project.</p>
<p>⚠️ <strong>Note</strong>: Make sure you have all the tools installed before starting to work with AWS services. Otherwise, you may encounter issues during deployment.</p>
<h3 id="content">Content</h3>
<ul>
<li><a href="/workshopFCJ/2-prerequiste/2.1-installnodejs/">Install NodeJS</a></li>
<li><a href="/workshopFCJ/2-prerequiste/2.2-installyarn/">Install Yarn for frontend</a></li>
<li><a href="/workshopFCJ/2-prerequiste/2.3-installsamcli/">Install SAM CLI for backend</a></li>
<li><a href="/workshopFCJ/2-prerequiste/2.4-createiam/">Create AWS Account &amp; Configure IAM</a></li>
<li><a href="/workshopFCJ/2-prerequiste/2.5-create-google-oauth2/">Create Google OAuth2 Project</a></li>
</ul>

        
        <ul class="page-list">
            
            <li>
                <a href="/workshopFCJ/2-prerequiste/2.1-installnodejs/">Installing NodeJS</a>
                
                <p><p>NodeJS is a powerful and popular JavaScript runtime environment, widely used for developing dynamic web applications. In this step, you will install NodeJS on your operating system to build your dynamic e-commerce website.</p>
<h4 id="step-1-install-nodejs">Step 1: Install NodeJS</h4>
<ol>
<li>
<p><strong>Download NodeJS from the official website:</strong></p>
<ul>
<li>Visit the official NodeJS website at <a href="https://nodejs.org/">Node.js</a> and download the LTS version for your operating system (Windows, macOS, or Linux).</li>
<li>Download the <strong>.msi</strong> file for Windows, <strong>.pkg</strong> for macOS, or <strong>.tar.xz</strong> for Linux.</li>
</ul>
</li>
<li>
<p><strong>Install NodeJS on Windows:</strong></p></p>
                
            </li>
            
            <li>
                <a href="/workshopFCJ/2-prerequiste/2.2-installyarn/">Installing Yarn</a>
                
                <p><p>Yarn is a package manager for JavaScript that is widely used in frontend projects to efficiently manage libraries and software packages. Yarn helps address issues related to installation speed, consistency, and security when managing project dependencies.</p>
<h4 id="step-1-install-yarn">Step 1: Install Yarn</h4>
<ol>
<li>
<p><strong>Install Yarn via npm:</strong>+</p>
<ul>
<li>
<p>Yarn can be installed through npm (Node Package Manager). Make sure you have <strong>NodeJS</strong> and <strong>npm</strong> installed before proceeding.</p>
</li>
<li>
<p>To install Yarn, open your terminal or Command Prompt and run the following command:</p></p>
                
            </li>
            
            <li>
                <a href="/workshopFCJ/2-prerequiste/2.3-installsamcli/">Installing SAM CLI</a>
                
                <p><p>The AWS SAM CLI is a command-line tool that helps you develop, package, and deploy serverless applications using AWS Lambda, API Gateway, and other AWS serverless services. In this step, you will install the SAM CLI to develop the serverless backend for your dynamic e-commerce website.</p>
<h4 id="step-1-install-the-sam-cli">Step 1: Install the SAM CLI</h4>
<ol>
<li>
<p><strong>Install on Windows:</strong></p>
<ul>
<li>Go to the <a href="https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/install-sam-cli.html">Installing the AWS SAM CLI</a> page to download the SAM CLI installer for Windows.</li>
<li>Select Windows.</li>
<li>Run the <code>.msi</code> installer and follow the instructions to complete the installation.</li>
<li>Open your terminal and enter <code>sam --version</code>.</li>
</ul>
<p><img src="/images/install_samcli.png" alt="Error image"></p></p>
                
            </li>
            
            <li>
                <a href="/workshopFCJ/2-prerequiste/2.4-createiam/">Create Account and Configure IAM</a>
                
                <p><h4 id="create-account-and-configure-iam">Create Account and Configure IAM</h4>
<p>AWS Identity and Access Management (IAM) allows you to securely manage access to AWS resources. In this step, you will create an IAM user, grant appropriate permissions, and configure an Access Key for use in subsequent steps of the workshop.</p>
<h4 id="create-iam-user">Create IAM User</h4>
<ol>
<li>
<p>Go to the <a href="https://aws.amazon.com/console/">AWS Management Console</a> and navigate to the <strong>IAM</strong> service.</p>
<p><img src="/images/iam_access.png" alt="Error image"></p>
</li>
<li>
<p>In the left navigation pane, select <strong>Users</strong> and click <strong>Create users</strong> from the left menu.</p></p>
                
            </li>
            
            <li>
                <a href="/workshopFCJ/2-prerequiste/2.5-create-google-oauth2/">Create Google OAuth2 Project</a>
                
                <p><h4 id="create-google-oauth2-project">Create Google OAuth2 Project</h4>
<p>To integrate Google Sign-In functionality into your e-commerce website, you need to register and create a Google OAuth2 Project.</p>
<p><strong>Steps to follow:</strong></p>
<ol>
<li>
<p><strong>Access Google Cloud Console</strong></p>
<ul>
<li>Open <a href="https://console.cloud.google.com/">Google Cloud Console</a>.</li>
<li>Log in with your Google account.</li>
</ul>
</li>
<li>
<p><strong>Create a New Project (if you don’t have one yet)</strong></p>
<ul>
<li>
<p>Click <strong>Select a project</strong> &gt; <strong>New Project</strong>.
<img src="/images/create_project_google_auth.png" alt="create_project_google_auth"></p>
</li>
<li>
<p>Enter a project name, choose the location, and click <strong>Create</strong>.
<img src="/images/create_FcjFashionShop_ggouth.png" alt="create_FcjFashionShop_ggouth"></p>
</li>
<li>
<p>After the project is created, click <strong>Select a project</strong>.
<img src="/images/select_FcjFashionShop.png" alt="create_FcjFashionShop_ggouth"></p></p>
                
            </li>
            
        </ul>
        
    </div>
</article>

        </main>
    </div>
</body>
</html>
