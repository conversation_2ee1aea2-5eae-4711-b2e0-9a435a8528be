<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Testing Backend APIs with Postman :: AWS System Manager</title>
    <link rel="stylesheet" href="/workshopFCJ/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <nav class="sidebar">
            <div class="logo">
    <a href="https://thuananwork.github.io/workshopFCJ/">
        <h2>AWS System Manager</h2>
    </a>
</div>

<nav class="menu">
    <ul>
        
    </ul>
    
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/1-introduce/">
            <strong>false. Introduction</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/">
            <strong>false. Preparation</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.1-installnodejs/">
            false. Installing NodeJS
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.2-installyarn/">
            false. Installing Yarn
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.3-installsamcli/">
            false. Installing SAM CLI
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.4-createiam/">
            false. Create Account and Configure IAM
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/2-prerequiste/2.5-create-google-oauth2/">
            false. Create Google OAuth2 Project
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.1-installnodejs/">
            <strong>false. Installing NodeJS</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.2-installyarn/">
            <strong>false. Installing Yarn</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.3-installsamcli/">
            <strong>false. Installing SAM CLI</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.4-createiam/">
            <strong>false. Create Account and Configure IAM</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/2-prerequiste/2.5-create-google-oauth2/">
            <strong>false. Create Google OAuth2 Project</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/3-deploybackend/">
            <strong>false. Deploying the Backend</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/3-deploybackend/3.1-deploy-backend/">
            false. Deploying the Backend with SAM CLI/CloudFormation
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/3-deploybackend/3.2-check-status-log-backend/">
            false. Checking Backend Status and Logs After Deployment
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/3-deploybackend/3.1-deploy-backend/">
            <strong>false. Deploying the Backend with SAM CLI/CloudFormation</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/3-deploybackend/3.2-check-status-log-backend/">
            <strong>false. Checking Backend Status and Logs After Deployment</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/4-testbackendapi/">
            <strong>false. Testing Backend APIs with Postman</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/4-testbackendapi/4.1-endpoint-api-gateway/">
            false. Retrieving the API Gateway Endpoint
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/4-testbackendapi/4.2-get-post-response/">
            false. Sending GET/POST Requests to Verify Backend Responses
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/4-testbackendapi/4.1-endpoint-api-gateway/">
            <strong>false. Retrieving the API Gateway Endpoint</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/4-testbackendapi/4.2-get-post-response/">
            <strong>false. Sending GET/POST Requests to Verify Backend Responses</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/">
            <strong>false. Deploy Frontend</strong>
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.1-frontend-s3/">
            false. Deploy Frontend to S3 Bucket
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.2-enable-static-hosting/">
            false. Enable Static Hosting, Configure CORS, and Set S3 Bucket Permissions
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.3-s3-bucket-permission/">
            false. Grant Public Permissions to S3 Bucket
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.4-clientid-clientserver/">
            false. Configure Google OAuth2 Client ID and Client Secret
        </a>
    </li>
    
    <li class="page-item ">
        <a href="/workshopFCJ/5-deployfrontend/5.5-connect-frontend-api-backend/">
            false. Connect the Frontend to the Backend API
        </a>
    </li>
    
    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.1-frontend-s3/">
            <strong>false. Deploy Frontend to S3 Bucket</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.2-enable-static-hosting/">
            <strong>false. Enable Static Hosting, Configure CORS, and Set S3 Bucket Permissions</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.3-s3-bucket-permission/">
            <strong>false. Grant Public Permissions to S3 Bucket</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.4-clientid-clientserver/">
            <strong>false. Configure Google OAuth2 Client ID and Client Secret</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/5-deployfrontend/5.5-connect-frontend-api-backend/">
            <strong>false. Connect the Frontend to the Backend API</strong>
        </a>
    </li>
    
    
</ul>

    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/6-ssl-s3-static/">
            <strong>false. Setup SSL S3 Static Website</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/7-demo/">
            <strong>false. Demo and Run the Project</strong>
        </a>
    </li>
    
    
</ul>

    
        


<ul class="section">
    <li class="section-title">
        <a href="/workshopFCJ/8-cleanup/">
            <strong>false. Clean Up Resources</strong>
        </a>
    </li>
    
    
</ul>

    
</nav>



        </nav>
        <main class="content">
            
<article>
    <header>
        <h1>Testing Backend APIs with Postman</h1>
    </header>
    <div class="article-content">
        <h4 id="testing-backend-apis-with-postman">Testing Backend APIs with Postman</h4>
<p>After successfully deploying your backend with <strong>API Gateway</strong> and <strong>Lambda</strong>, the next step is to test your APIs to verify that the backend system is functioning correctly before integrating with the frontend.</p>
<p><strong>Postman</strong> is a powerful and widely-used tool that allows you to send requests (GET, POST, PUT, DELETE, &hellip;) to API endpoints, view responses, verify processing logic, and debug errors.</p>
<p>You can download Postman here: <a href="https://www.postman.com/downloads/"><strong>Download Postman</strong></a></p>
<p><strong>Objectives of this section:</strong></p>
<ul>
<li>Guide you on how to retrieve the <strong>API Gateway endpoint</strong> that you just deployed on AWS to use with Postman.</li>
<li>Practice sending <strong>GET/POST</strong> requests to the API, analyze the returned responses to verify backend correctness.</li>
<li>Identify and troubleshoot common errors encountered during API testing.</li>
</ul>
<p><strong>This section covers:</strong></p>
<ol>
<li><strong>Retrieving the API Gateway endpoint:</strong><br>
Instructions on how to locate and copy the endpoint URL from the AWS Console for API testing purposes.</li>
<li><strong>Sending GET/POST requests to verify backend responses:</strong><br>
Step-by-step guidance on how to send GET/POST requests with Postman, enter payload data, and review backend responses.</li>
</ol>
<div class="notice notice-info">
    <div class="notice-icon">
<pre><code>        &lt;i class=&quot;fas fa-info-circle&quot;&gt;&lt;/i&gt;
    
&lt;/div&gt;
&lt;div class=&quot;notice-content&quot;&gt;
    &lt;strong&gt;Note:&lt;/strong&gt;&lt;br&gt;
</code></pre>
<p>Before testing APIs with Postman, make sure that the backend has been deployed successfully and there are no errors from previous steps (CloudFormation/Lambda must be in a successful state).
</div></p>
</div>
<p><strong>By the end of this section, you will:</strong></p>
<ul>
<li>Know how to obtain the API Gateway endpoint for testing.</li>
<li>Be proficient in using Postman to send GET/POST requests to your backend.</li>
<li>Be able to analyze and verify API responses, and be ready to connect your tested backend with the frontend.</li>
</ul>
<h3 id="contents">Contents</h3>
<ul>
<li><a href="4.1-api-gateway-endpoint/">Retrieve the API Gateway endpoint</a></li>
<li><a href="/workshopFCJ/4-testbackendapi/4.2-get-post-response/">Send GET/POST requests to verify backend responses</a></li>
</ul>

        
        <ul class="page-list">
            
            <li>
                <a href="/workshopFCJ/4-testbackendapi/4.1-endpoint-api-gateway/">Retrieving the API Gateway Endpoint</a>
                
                <p><h4 id="retrieving-the-api-gateway-endpoint">Retrieving the API Gateway Endpoint</h4>
<p>After deploying the backend using SAM CLI/CloudFormation, the system will automatically generate an endpoint address for your API Gateway. This is the address you will use to send test requests via Postman, curl, or integrate with your frontend later.</p>
<div class="notice notice-tip">
    <div class="notice-icon">
<pre><code>        &lt;i class=&quot;fas fa-lightbulb&quot;&gt;&lt;/i&gt;
    
&lt;/div&gt;
&lt;div class=&quot;notice-content&quot;&gt;
    &lt;strong&gt;Note:&lt;/strong&gt;&lt;br&gt;
</code></pre>
<p>Use the previously saved endpoint if you already have it.
</div></p>
</div>
<p><strong>Steps to follow:</strong></p>
<ol>
<li>
<p><strong>Access the AWS Management Console</strong></p></p>
                
            </li>
            
            <li>
                <a href="/workshopFCJ/4-testbackendapi/4.2-get-post-response/">Sending GET/POST Requests to Verify Backend Responses</a>
                
                <p><h4 id="sending-getpost-requests-to-verify-backend-login-responses">Sending GET/POST Requests to Verify Backend Login Responses</h4>
<p>After obtaining the API Gateway endpoint, you need to send <strong>POST</strong> and <strong>GET</strong> requests to the API to confirm that the backend is functioning properly. This is typically done using <strong>Postman</strong>, the most popular API testing tool.</p>
<p><strong>Steps to follow:</strong></p>
<ol>
<li>
<p><strong>Testing POST Method with Postman</strong></p>
<ul>
<li>
<p>Open Postman, select <strong>New</strong> → <strong>HTTP Request</strong>.</p>
</li>
<li>
<p>Set the method to <strong>POST</strong>.</p>
</li>
<li>
<p>Enter the full endpoint URL, for example:</p></p>
                
            </li>
            
        </ul>
        
    </div>
</article>

        </main>
    </div>
</body>
</html>
