box: golang
build:
  steps:
    # Gets the dependencies
    - script:
        name: get hugo
        code: |
          git clone https://github.com/gohugoio/hugo.git && cd hugo && go install
    # Sets the go workspace and places you package
    # at the right place in the workspace tree
    - setup-go-workspace
    # Build the project
    - script:
        name: build site
        code: |
          cd exampleSite && hugo