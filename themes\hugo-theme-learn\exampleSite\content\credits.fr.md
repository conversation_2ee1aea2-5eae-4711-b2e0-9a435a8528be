---
title: Cré<PERSON>s
disableToc: true
---

## Contributeurs

Merci à eux <i class="fas fa-heart"></i> de rendre le monde Open Source meilleur  !

{{% ghcontributors "https://api.github.com/repos/matcornic/hugo-theme-learn/contributors?per_page=100" %}}

Et un grand merci à [@vjeantet](https://github.com/vjeantet) pour son travail sur [docdock](https://github.com/vjeantet/hugo-theme-docdock), un fork de _hugo-theme-learn_. La v2.0.0 du thème est en grande partie inspirée de son travail.

## Packages et librairies
* [mermaid](https://mermaid-js.github.io/) - géneration de diagrames et graphiques à partir de texte similaire à Markdown
* [font awesome](http://fontawesome.io/) - Le framework de polices iconiques
* [jQuery](https://jquery.com) - La plus connue des librairies Javascript
* [lunr](https://lunrjs.com) - Lunr fournit des fonctions de recherche sans service externe
* [horsey](https://bevacqua.github.io/horsey/) - Autocomplétion de composants (utiliser pour les suggestions de recherche)
* [clipboard.js](https://zenorocha.github.io/clipboard.js) - Copier le texte dans le presse-papier
* [highlight.js](https://highlightjs.org) - Mise en valeur de syntaxes
* [modernizr](https://modernizr.com) - Une boite à outil Javascript qui permet aux développeurs d'utiliser les dernières fonctionnalités de CSS et HTML5, même sur de vieux navigateurs.

## Outils

* [Netlify](https://www.netlify.com) - Déploiement continue et hébergement de cette documentation
* [Hugo](https://gohugo.io/)

