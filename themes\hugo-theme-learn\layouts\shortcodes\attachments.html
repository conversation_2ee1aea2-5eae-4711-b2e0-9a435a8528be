{{ $_hugo_config := `{ "version": 1 }` -}}
<section class="attachments {{ with .Get "style"}}{{.}}{{ end }}">
  <label>
    <i class="fas fa-paperclip" aria-hidden="true"></i>
    {{with .Get "title"}}{{.}}{{else}}{{T "Attachments-label"}}{{end}}
  </label>
  <div class="attachments-files">
  {{- $filesName := "files" }}
  {{- if ne .Page.File.BaseFileName "index" }}
    {{- $filesName = printf "%s.files" .Page.File.BaseFileName }}
  {{- end}}
  {{- $fileDir := replace .Page.File.Dir "\\" "/" }}
  {{- $pattern := .Get "pattern" | default "" }}
  {{- range (readDir (printf "content/%s%s" .Page.File.Dir $filesName) ) }}
    {{- if findRE $pattern .Name}}
      {{- $size := .Size }}
      {{- $unit := "Byte" }}
      {{- if ge $size 1024 }}
        {{- $size = div $size 1024 }}
        {{- $unit = "Kilobyte" }}
      {{- end}}
      {{- if ge $size 1024 }}
        {{- $size = div $size 1024 }}
        {{- $unit = "Megabyte" }}
      {{- end}}
    <li>
      <a href="{{ (printf "%s%s/%s" $fileDir $filesName .Name) | relLangURL }}">{{.Name}}</a>
      ({{$size}} {{T (print $unit "-symbol")}})
    </li>
    {{- end}}
  {{- end}}
  </div>
  {{- .Inner}}
</section>
